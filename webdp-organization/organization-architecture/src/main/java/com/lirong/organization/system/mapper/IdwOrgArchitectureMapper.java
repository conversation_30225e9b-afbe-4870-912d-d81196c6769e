package com.lirong.organization.system.mapper;

import java.util.List;

import com.lirong.common.core.domain.Ztree;
import com.lirong.organization.system.domain.IdwOrgArchitecture;
import org.apache.ibatis.annotations.Param;

/**
 * 机构架构Mapper接口
 *
 * <AUTHOR>
 * @date 2023-11-07
 */
public interface IdwOrgArchitectureMapper {
    /**
     * 查询机构架构
     *
     * @param architectureId 机构架构ID
     * @return 机构架构
     */
    public IdwOrgArchitecture selectIdwOrgArchitectureById(Long architectureId);

    /**
     * 查询机构架构列表
     *
     * @param idwOrgArchitecture 机构架构
     * @return 机构架构集合
     */
    public List<IdwOrgArchitecture> selectIdwOrgArchitectureList(IdwOrgArchitecture idwOrgArchitecture);

    /**
     * 新增机构架构
     *
     * @param idwOrgArchitecture 机构架构
     * @return 结果
     */
    public int insertIdwOrgArchitecture(IdwOrgArchitecture idwOrgArchitecture);

    /**
     * 修改机构架构
     *
     * @param idwOrgArchitecture 机构架构
     * @return 结果
     */
    public int updateIdwOrgArchitecture(IdwOrgArchitecture idwOrgArchitecture);

    /**
     * 根据机构架构ID删除
     *
     * @param architectureId ID
     * @return 结果
     */
    public int deleteIdwOrgArchitectureById(@Param("architectureId") Long architectureId, @Param("loginName") String loginName);

    /**
     * 根据机构编码查询组织架构树
     *
     * @param orgCode 机构编码
     * @return 结果
     */
    public List<Ztree> selectOrgArchitectureChartByOrgCode(String orgCode);

    /**
     * 根据ID校验当前节点是否存在子集
     *
     * @param architectureId ID
     * @return 结果
     */
    public boolean selectOrgArchitectureIsParent(Long architectureId);

    /**
     * 查询机构架构树并排除ID及其子集
     *
     * @param orgCode        机构编码
     * @param architectureId ID
     * @return 结果
     */
    public List<Ztree> selectArchitectureTreeExclude(@Param("orgCode") String orgCode, @Param("architectureId") Long architectureId);

    /**
     * 根据ID查询所有子集
     *
     * @param architectureId ID
     * @return 结果
     */
    public List<IdwOrgArchitecture> selectIdwOrgArchitectureChildrenById(Long architectureId);
}
