package com.lirong.organization.operational.controller;

import com.lirong.common.annotation.Log;
import com.lirong.common.config.WebdpConfig;
import com.lirong.common.core.domain.AjaxResult;
import com.lirong.common.enums.BusinessType;
import com.lirong.common.utils.CacheUtils;
import com.lirong.common.utils.ShiroUtils;
import com.lirong.common.utils.StringUtils;
import com.lirong.common.utils.file.FileUploadUtils;
import com.lirong.common.utils.file.FileUtils;
import com.lirong.common.utils.poi.ExcelUtil;
import com.lirong.multimedia.domain.IdwMultimedia;
import com.lirong.multimedia.service.IdwMultimediaService;
import com.lirong.organization.common.domain.IdwOrg;
import com.lirong.organization.common.service.IdwOrgService;
import com.lirong.organization.history.domain.IdwOrgDevelopmentHistory;
import com.lirong.organization.history.service.IdwOrgDevelopmentHistoryService;
import com.lirong.organization.news.domain.IdwOrgNews;
import com.lirong.organization.news.service.IdwOrgNewsService;
import com.lirong.organization.operational.domain.IdwOperational;
import com.lirong.organization.staff.domain.IdwOrgStaff;
import com.lirong.organization.staff.service.IdwOrgStaffService;
import com.lirong.organization.weapon.domain.IdwOrgWeapon;
import com.lirong.organization.weapon.service.IdwOrgWeaponService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.lirong.organization.operational.service.IdwOperationalService;
import com.lirong.common.core.controller.BaseController;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 国防单位Controller
 *
 * <AUTHOR>
 * @date 2021-01-29
 */
@Controller
@RequestMapping("/organization/operational")
public class IdwOperationalController extends BaseController {

    private String prefix = "organization/operational";

    @Autowired//组织机构
    private IdwOrgService idwOrgService;
    @Autowired//机构人员
    private IdwOrgStaffService idwOrgStaffService;
    @Autowired//机构新闻报道
    private IdwOrgNewsService idwOrgNewsService;
    @Autowired//机构武器装备
    private IdwOrgWeaponService idwOrgWeaponService;
    @Autowired//国防单位
    private IdwOperationalService idwOperationalService;
    @Autowired//图片视频
    private IdwMultimediaService idwMultimediaService;
    @Autowired//机构发展历程
    private IdwOrgDevelopmentHistoryService idwOrgDevelopmentHistoryService;

    @RequiresPermissions("organization:operational:view")
    @GetMapping()
    public String operational() {
        return prefix + "/operationalList";
    }

    /**
     * 新增国防单位
     *
     * @return 结果
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/addOperational";
    }

    /**
     * 修改国防单位跳转选项卡页面
     */
    @GetMapping("/edit/{orgCode}")
    public String editTabls(@PathVariable("orgCode") String orgCode, ModelMap mmap) {
        mmap.put("orgCode", orgCode);
        return prefix + "/operationalTabs";
    }

    /**
     * 修改国防单位跳转修改页面
     */
    @GetMapping("/operationalEdit/{orgCode}")
    public String researchEdit(@PathVariable("orgCode") String orgCode, ModelMap mmap) {
        IdwOrg org = idwOrgService.selectOrgByOrgCode(orgCode);
        mmap.put("idwOperational", org);
        mmap.put("avatar", StringUtils.isNotBlank(org.getAvatar()) ? org.getAvatar() : "");
        return prefix + "/editOperational";
    }

    /**
     * 机构相关文件上传保存
     */
    @RequiresPermissions("organization:operational:operationalTabs")
    @PostMapping(value = {"/saveBatchUploadFile"})
    @ResponseBody
    public AjaxResult saveBatchUploadFile(String fileName, String fileUrl) {
        return AjaxResult.success(idwOperationalService.saveBatchUploadFile(fileName, fileUrl));
    }

    /**
     * 导入下载模板
     *
     * @return 结果
     */
    @RequiresPermissions("organization:operational:import")
    @GetMapping("/importTemplate")
    @ResponseBody
    public AjaxResult importTemplate() {
        ExcelUtil<IdwOperational> util = new ExcelUtil<IdwOperational>(IdwOperational.class);
        return util.importTemplateExcel("国防单位");
    }

    /**
     * Excel导入跳转页面
     */
    @RequiresPermissions("organization:operational:import")
    @GetMapping("/importExcel")
    public String importExcel() {
        return prefix + "/importExcel";
    }

    /**
     * 导入
     *
     * @param excelFile     数据列表
     * @param zipFile       相关文件
     * @param updateSupport 是否支持更新, 如果已存在, 则进行更新
     * @return 结果
     * @throws Exception
     */
    @Log(title = "国防单位", businessType = BusinessType.IMPORT)
    @RequiresPermissions("organization:operational:import")
    @PostMapping("/importData")
    @ResponseBody
    public Map<String, Object> importData(MultipartFile excelFile, MultipartFile zipFile, boolean updateSupport) throws Exception {
        String userName = ShiroUtils.getUserName();
        Map<String, Object> msgMap = new HashMap<>();
        //解压压缩包
        //key 文件在压缩包中的相对路径 value 文件对应在filePathList中的索引
        Map<String, Integer> filePathIndexMap = new HashMap<>();
        //文件路径
        List<String> filePathList = new ArrayList<>();
        //临时文件夹路径
        String baseDir = null;
        if (StringUtils.isNotNull(zipFile)) {
            Map<String, Object> map = null;
            try {
                map = FileUploadUtils.decompressionZipImage(zipFile);
            } catch (IOException e) {
                e.printStackTrace();
            }
            filePathIndexMap = (Map<String, Integer>) map.get("filePathIndexMap");
            filePathList = (List<String>) map.get("filePath");
            baseDir = (String) map.get("baseDir");
        }
        //校验结果
        List<String> formatErrorMessage = new ArrayList<>();
        //校验数据格式
        ExcelUtil<IdwOperational> operationalUtil = new ExcelUtil<IdwOperational>(IdwOperational.class);
        List<IdwOperational> operationalList = operationalUtil.importExcel("国防单位", excelFile.getInputStream());
        List<String> orgMessage = idwOperationalService.verifyImportOperational(operationalList, filePathIndexMap, filePathList, baseDir, updateSupport);
        if (StringUtils.isNotNull(orgMessage)) {
            formatErrorMessage.addAll(orgMessage);
        }
        ExcelUtil<IdwOrgWeapon> orgWeaponUtil = new ExcelUtil<IdwOrgWeapon>(IdwOrgWeapon.class);
        List<IdwOrgWeapon> orgWeaponList = orgWeaponUtil.importExcel("武器装备", excelFile.getInputStream());
        List<String> orgWeaponMessage = idwOrgWeaponService.verifyImportOrgWeapon(orgWeaponList);
        if (StringUtils.isNotNull(orgWeaponMessage)) {
            formatErrorMessage.addAll(orgWeaponMessage);
        }
        ExcelUtil<IdwOrgStaff> orgStaffUtil = new ExcelUtil<IdwOrgStaff>(IdwOrgStaff.class);
        List<IdwOrgStaff> orgStaffList = orgStaffUtil.importExcel("主要人员", excelFile.getInputStream());
        List<String> orgStaffMessage = idwOrgStaffService.verifyImportOrgStaff(orgStaffList, filePathIndexMap, filePathList, baseDir, updateSupport, "主要人员");
        if (StringUtils.isNotNull(orgStaffMessage)) {
            formatErrorMessage.addAll(orgStaffMessage);
        }
        ExcelUtil<IdwOrgDevelopmentHistory> orgDevelopmentHistoryUtil = new ExcelUtil<IdwOrgDevelopmentHistory>(IdwOrgDevelopmentHistory.class);
        List<IdwOrgDevelopmentHistory> orgDevelopmentHistoryList = orgDevelopmentHistoryUtil.importExcel("发展历程", excelFile.getInputStream());
        List<String> orgDevelopmentHistoryMessage = idwOrgDevelopmentHistoryService.verifyImportOrgDevelopmentHistory(orgDevelopmentHistoryList);
        if (StringUtils.isNotNull(orgDevelopmentHistoryMessage)) {
            formatErrorMessage.addAll(orgDevelopmentHistoryMessage);
        }
        ExcelUtil<IdwOrgNews> orgNewsUtil = new ExcelUtil<IdwOrgNews>(IdwOrgNews.class);
        List<IdwOrgNews> orgNewsList = orgNewsUtil.importExcel("新闻报道", excelFile.getInputStream());
        List<String> orgNewsMessage = idwOrgNewsService.verifyImportOrgNews(orgNewsList);
        if (StringUtils.isNotNull(orgNewsMessage)) {
            formatErrorMessage.addAll(orgNewsMessage);
        }
        //清除机构编码缓存
        CacheUtils.remove("orgImportTreatingAfterOrgCodeList-" + userName);

        //不为空 出现格式错误
        if (formatErrorMessage.size() > 0) {
            //清除缓存
            CacheUtils.remove("orgImportTreatingTemporaryFilePathList-" + userName);
            CacheUtils.remove("orgImportTreatingNewFilePathList-" + userName);
            //删除临时文件夹
            if (StringUtils.isNotBlank(baseDir)) {
                FileUploadUtils.deleteDir(new File(baseDir));
            }
            msgMap.put("status", false);
            msgMap.put("msg", formatErrorMessage);
            return msgMap;
        }

        //处理文件
        //需要拷贝的文件
        List<String> temporaryFilePathList = (List<String>) CacheUtils.get("orgImportTreatingTemporaryFilePathList-" + userName);
        List<String> newFilePathList = (List<String>) CacheUtils.get("orgImportTreatingNewFilePathList-" + userName);
        if (StringUtils.isNotNull(temporaryFilePathList) && temporaryFilePathList.size() > 0) {
            FileUploadUtils.batchCopyFile(temporaryFilePathList, newFilePathList);
        }
        CacheUtils.remove("orgImportTreatingTemporaryFilePathList-" + userName);
        CacheUtils.remove("orgImportTreatingNewFilePathList-" + userName);
        //删除临时文件夹
        if (StringUtils.isNotBlank(baseDir)) {
            FileUploadUtils.deleteDir(new File(baseDir));
        }
        //提示信息
        List<String> formatSuccessMessage = new ArrayList<>();
        //数据格式无误 导入数据
        String operationalSuccessMessage = idwOperationalService.importOperational(updateSupport, userName);
        if (StringUtils.isNotBlank(operationalSuccessMessage)) {
            formatSuccessMessage.add(operationalSuccessMessage);
        }
        //武器装备
        String operationalWeaponSuccessMessage = idwOrgWeaponService.importOrgWeapon(updateSupport, userName);
        if (StringUtils.isNotBlank(operationalWeaponSuccessMessage)) {
            formatSuccessMessage.add(operationalWeaponSuccessMessage);
        }
        //机构人员
        String operationalStaffSuccessMessage = idwOrgStaffService.importOrgStaff(updateSupport, userName, "主要人员");
        if (StringUtils.isNotBlank(operationalStaffSuccessMessage)) {
            formatSuccessMessage.add(operationalStaffSuccessMessage);
        }
        //发展历程
        String operationalDevelopmentHistorySuccessMessage = idwOrgDevelopmentHistoryService.importOrgDevelopmentHistory(updateSupport, userName);
        if (StringUtils.isNotBlank(operationalDevelopmentHistorySuccessMessage)) {
            formatSuccessMessage.add(operationalDevelopmentHistorySuccessMessage);
        }
        //新闻报道
        String operationalNewsSuccessMessage = idwOrgNewsService.importOrgNews(updateSupport, userName);
        if (StringUtils.isNotBlank(operationalNewsSuccessMessage)) {
            formatSuccessMessage.add(operationalNewsSuccessMessage);
        }
        if (formatSuccessMessage.size() < 1) {
            //错误Excel
            formatErrorMessage.add("模板错误,,");
            msgMap.put("status", false);
            msgMap.put("msg", formatErrorMessage);
            return msgMap;
        }

        msgMap.put("status", true);
        msgMap.put("msg", formatSuccessMessage);
        return msgMap;
    }

    /**
     * 根据机构编码导出国防单位
     */
    @RequiresPermissions("organization:operational:export")
    @Log(title = "国防单位", businessType = BusinessType.EXPORT)
    @PostMapping("/exportExcel")
    @ResponseBody
    public AjaxResult exportExcel(String[] orgCodes) {
        //压缩文件路径
        List<String> filePathList = new ArrayList<>();
        //Excel文件路径
        List<String> excelFilePath = new ArrayList<>();
        //机构
        List<IdwOperational> orgList = idwOperationalService.selectByOrgCodes(orgCodes);
        List<String> orgAvatarList = orgList.stream().filter(org -> StringUtils.isNotBlank(org.getAvatar())).map(IdwOperational::getAvatar).collect(Collectors.toList());
        if (orgAvatarList.size() > 0) {
            filePathList.addAll(orgAvatarList);
        }
        ExcelUtil<IdwOperational> orgUtil = new ExcelUtil<IdwOperational>(IdwOperational.class);
        String orgFileName = (String) orgUtil.exportExcel(orgList, "国防单位").get("msg");
        String orgExcelFilePath = WebdpConfig.getDownloadPath() + orgFileName;
        filePathList.add(orgExcelFilePath);
        excelFilePath.add(orgExcelFilePath);
        //机构人员
        List<IdwOrgStaff> orgStaffList = idwOrgStaffService.selectByOrgCodes(orgCodes);
        if (orgStaffList.size() > 0) {
            List<String> orgStaffAvatarList = orgStaffList.stream().filter(staff -> StringUtils.isNotBlank(staff.getAvatar())).map(IdwOrgStaff::getAvatar).collect(Collectors.toList());
            filePathList.addAll(orgStaffAvatarList);//保存机构人员头像

            if (orgStaffAvatarList.size() > 0) {
                filePathList.addAll(orgStaffAvatarList);//保存机构人员头像
            }
            ExcelUtil<IdwOrgStaff> orgStaffExcelUtil = new ExcelUtil<IdwOrgStaff>(IdwOrgStaff.class);
            String orgStaffFileName = (String) orgStaffExcelUtil.exportExcel(orgStaffList, "机构人员").get("msg");
            String orgStaffExcelFilePath = WebdpConfig.getDownloadPath() + orgStaffFileName;
            filePathList.add(orgStaffExcelFilePath);
            excelFilePath.add(orgStaffExcelFilePath);
        }
        //发展历程
        List<IdwOrgDevelopmentHistory> orgDevelopmentHistoryList = idwOrgDevelopmentHistoryService.selectByOrgCodes(orgCodes);
        if (orgDevelopmentHistoryList.size() > 0) {
            ExcelUtil<IdwOrgDevelopmentHistory> orgDevelopmentHistoryExcelUtil = new ExcelUtil<IdwOrgDevelopmentHistory>(IdwOrgDevelopmentHistory.class);
            String orgDevelopmentHistoryFileName = (String) orgDevelopmentHistoryExcelUtil.exportExcel(orgDevelopmentHistoryList, "发展历程").get("msg");
            String orgDevelopmentHistoryExcelFilePath = WebdpConfig.getDownloadPath() + orgDevelopmentHistoryFileName;
            filePathList.add(orgDevelopmentHistoryExcelFilePath);
            excelFilePath.add(orgDevelopmentHistoryExcelFilePath);
        }
        //武器装备
        List<IdwOrgWeapon> orgWeaponList = idwOrgWeaponService.selectByOrgCodes(orgCodes);
        if (orgWeaponList.size() > 0) {
            List<String> orgWeaponAvatarList = orgWeaponList.stream().filter(weapon -> StringUtils.isNotBlank(weapon.getAvatar())).map(IdwOrgWeapon::getAvatar).collect(Collectors.toList());
            //保存机构武器装备图片
            if (orgWeaponAvatarList.size() > 0) {
                filePathList.addAll(orgWeaponAvatarList);
            }
            ExcelUtil<IdwOrgWeapon> orgWeaponExcelUtil = new ExcelUtil<IdwOrgWeapon>(IdwOrgWeapon.class);
            String orgWeaponFileName = (String) orgWeaponExcelUtil.exportExcel(orgWeaponList, "武器装备").get("msg");
            String orgWeaponExcelFilePath = WebdpConfig.getDownloadPath() + orgWeaponFileName;
            filePathList.add(orgWeaponExcelFilePath);
            excelFilePath.add(orgWeaponExcelFilePath);
        }
        //新闻报道
        List<IdwOrgNews> orgNewsList = idwOrgNewsService.selectByOrgCodes(orgCodes);
        if (orgNewsList.size() > 0) {
            List<String> orgNewsThumbnailList = orgNewsList.stream().filter(news -> StringUtils.isNotBlank(news.getThumbnail())).map(IdwOrgNews::getThumbnail).collect(Collectors.toList());
            //保存新闻报道缩略图
            if (orgNewsThumbnailList.size() > 0) {
                filePathList.addAll(orgNewsThumbnailList);
            }
            ExcelUtil<IdwOrgNews> orgNewsExcelUtil = new ExcelUtil<IdwOrgNews>(IdwOrgNews.class);
            String orgNewsFileName = (String) orgNewsExcelUtil.exportExcel(orgNewsList, "新闻报道").get("msg");
            String orgNewsExcelFilePath = WebdpConfig.getDownloadPath() + orgNewsFileName;
            filePathList.add(orgNewsExcelFilePath);
            excelFilePath.add(orgNewsExcelFilePath);
        }

        //图片视频
        List<IdwMultimedia> orgMultimediaList = idwMultimediaService.selectByAssociationCodes(orgCodes, "organization");
        if (orgMultimediaList.size() > 0) {
            List<String> orgMultimediaStoragePathList = orgMultimediaList.stream().filter(m -> StringUtils.isNotBlank(m.getStoragePath())).map(IdwMultimedia::getStoragePath).collect(Collectors.toList());
            //保存图片视频
            if (orgMultimediaStoragePathList.size() > 0) {
                filePathList.addAll(orgMultimediaStoragePathList);
            }
            List<String> orgMultimediaThumbnailList = orgMultimediaList.stream().filter(m -> StringUtils.isNotBlank(m.getThumbnail())).map(IdwMultimedia::getThumbnail).collect(Collectors.toList());
            //保存缩略图
            if (orgMultimediaThumbnailList.size() > 0) {
                filePathList.addAll(orgMultimediaThumbnailList);
            }
            ExcelUtil<IdwMultimedia> orgMultimediaExcelUtil = new ExcelUtil<IdwMultimedia>(IdwMultimedia.class);
            String orgMultimediaFileName = (String) orgMultimediaExcelUtil.exportExcel(orgMultimediaList, "图片视频 ").get("msg");
            //获取Excel路径
            String orgMultimediaExcelFilePath = WebdpConfig.getDownloadPath() + orgMultimediaFileName;
            filePathList.add(orgMultimediaExcelFilePath);
            excelFilePath.add(orgMultimediaExcelFilePath);
        }

        //拼接压缩包路径
        String zipFileName = UUID.randomUUID().toString() + "_国防单位.zip";
        String zipFilePath = WebdpConfig.getDownloadPath() + "/" + zipFileName;
        //将文件压缩
        FileUtils.multifileCompressionZip(filePathList, zipFilePath);
        //删除Excel
        FileUtils.batchDeleteFile(excelFilePath);
        return AjaxResult.success(zipFileName);
    }
}
