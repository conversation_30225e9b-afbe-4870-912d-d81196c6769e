package com.lirong.kg.controller;

import java.util.List;

import com.lirong.kg.service.ITaskService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.lirong.common.annotation.Log;
import com.lirong.common.enums.BusinessType;
import com.lirong.kg.domain.TaskMapping;
import com.lirong.kg.service.ITaskMappingService;
import com.lirong.common.core.controller.BaseController;
import com.lirong.common.core.domain.AjaxResult;
import com.lirong.common.utils.poi.ExcelUtil;
import com.lirong.common.core.page.TableDataInfo;

/**
 * 抽取映射Controller
 *
 * <AUTHOR>
 * @date 2022-03-21
 */
@Controller
@RequestMapping("/kg/mapping")
public class TaskMappingController extends BaseController {
    private String prefix = "kg/task/mapping";

    @Autowired
    private ITaskService taskService;
    @Autowired
    private ITaskMappingService taskMappingService;

    @RequiresPermissions("kg:mapping:view")
    @GetMapping()
    public String mapping() {
        return prefix + "/mapping";
    }

    /**
     * 查询抽取映射列表
     */
    @RequiresPermissions("kg:mapping:list")
    @PostMapping("/list/{taskId}")
    @ResponseBody
    public TableDataInfo list(@PathVariable("taskId") Long taskId) {
        startPage();
        TaskMapping taskMapping = new TaskMapping();
        taskMapping.setTaskId(taskId);
        List<TaskMapping> list = taskMappingService.selectTaskMappingList(taskMapping);
        return getDataTable(list);
    }

    /**
     * 导出抽取映射列表
     */
    @RequiresPermissions("kg:mapping:export")
    @Log(title = "抽取映射", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(TaskMapping taskMapping) {
        List<TaskMapping> list = taskMappingService.selectTaskMappingList(taskMapping);
        ExcelUtil<TaskMapping> util = new ExcelUtil<TaskMapping>(TaskMapping.class);
        return util.exportExcel(list, "mapping");
    }

    /**
     * 新增抽取映射
     */
    @GetMapping("/add/{taskId}")
    public String add(@PathVariable("taskId") Long taskId, ModelMap mmap) {
        List<TaskMapping> mappingList = taskService.queryUnMappingObject();
        mmap.put("mappingList", mappingList);
        mmap.put("taskId", taskId);
        return prefix + "/add";
    }

    /**
     * 新增保存抽取映射
     */
    @RequiresPermissions("kg:mapping:add")
    @Log(title = "抽取映射", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(TaskMapping taskMapping) {
        return toAjax(taskMappingService.insertTaskMapping(taskMapping));
    }

    /**
     * 修改抽取映射
     */
    @GetMapping("/edit/{mappingId}")
    public String edit(@PathVariable("mappingId") Long mappingId, ModelMap mmap) {
        TaskMapping taskMapping = taskMappingService.selectTaskMappingById(mappingId);
        mmap.put("taskMapping", taskMapping);
        return prefix + "/edit";
    }

    /**
     * 修改保存抽取映射
     */
    @RequiresPermissions("kg:mapping:edit")
    @Log(title = "抽取映射", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(TaskMapping taskMapping) {
        return toAjax(taskMappingService.updateTaskMapping(taskMapping));
    }

    /**
     * 删除抽取映射
     */
    @RequiresPermissions("kg:mapping:remove")
    @Log(title = "抽取映射", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(taskMappingService.deleteTaskMappingByIds(ids));
    }
}
