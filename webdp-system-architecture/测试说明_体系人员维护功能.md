# 体系人员维护功能测试说明

## 功能概述

在体系管理页面的操作列中添加了"人员维护"按钮，点击后会在新页签中打开体系结构人员管理页面，并自动选择对应的体系。

## 修改内容

### 1. 体系管理页面 (system.html)

#### 修改的文件
- `webdp-system-architecture/src/main/resources/templates/system/system/system.html`

#### 主要修改
1. **添加权限变量**：
   ```javascript
   let personnelFlag = [[${@permission.hasPermi('system:architecture:personnel:view')}]];
   ```

2. **修改操作列按钮**：
   - 将"人员管理"改为"人员维护"
   - 添加权限控制 `personnelFlag`
   - 改为蓝色按钮 `btn-primary`
   - 添加tooltip提示

3. **优化openPersonnelManagement函数**：
   ```javascript
   function openPersonnelManagement(systemId, systemName) {
       let personnelUrl = ctx + "system/architecture/personnel?systemId=" + systemId + "&systemName=" + encodeURIComponent(systemName);
       $.modal.openTab(systemName + ' - 人员维护', personnelUrl);
   }
   ```

### 2. 体系结构人员管理页面

#### 修改的文件
- `webdp-system-architecture/src/main/java/com/lirong/system/architecture/controller/IdwOrgSystemArchitecturePersonnelController.java`
- `webdp-system-architecture/src/main/resources/templates/system/architecture/personnel/systemArchitecturePersonnel.html`

#### 主要修改
1. **控制器支持参数传递**：
   ```java
   @GetMapping()
   public String index(@RequestParam(value = "systemId", required = false) Long systemId,
                      @RequestParam(value = "systemName", required = false) String systemName,
                      ModelMap mmap) {
       if (systemId != null) {
           mmap.put("systemId", systemId);
       }
       if (systemName != null) {
           mmap.put("systemName", systemName);
       }
       return prefix + "/systemArchitecturePersonnel";
   }
   ```

2. **前端自动选择体系**：
   - 从服务器端获取systemId和systemName参数
   - 在体系列表加载完成后自动选择对应体系
   - 添加调试信息便于排查问题

## 测试步骤

### 前置条件
1. 确保已执行菜单配置SQL：`sql/menu_system_architecture_personnel.sql`
2. 确保用户具有以下权限：
   - `system.system:view` - 查看体系列表
   - `system:architecture:personnel:view` - 查看体系结构人员管理

### 测试流程

#### 1. 访问体系管理页面
- 登录系统
- 导航到体系管理页面
- 确认页面正常显示体系列表

#### 2. 检查人员维护按钮
- 在体系列表的操作列中应该能看到"人员维护"按钮
- 按钮应该是蓝色的（btn-primary）
- 鼠标悬停应该显示"体系结构人员维护"提示

#### 3. 点击人员维护按钮
- 点击任意体系的"人员维护"按钮
- 应该在新页签中打开体系结构人员管理页面
- 页签标题应该是"[体系名称] - 人员维护"

#### 4. 验证指定体系模式
- 在新打开的页面中，左侧应该隐藏体系选择下拉框
- 显示"当前体系：[体系名称]"信息
- 页面标题应该显示为"[体系名称] - 体系结构"
- 体系结构树应该自动加载对应体系的数据
- 可以在浏览器控制台查看调试信息

#### 5. 测试人员管理功能
- 点击体系结构树中的机构节点
- 右侧应该显示机构信息和人员列表
- 测试添加、编辑、删除人员功能

## 预期结果

### 正常情况
1. **按钮显示**：人员维护按钮正常显示，有权限控制
2. **页签打开**：点击按钮后在新页签中打开人员管理页面
3. **指定体系模式**：自动进入指定体系模式，隐藏体系选择器，直接加载对应体系结构树
4. **功能完整**：所有人员管理功能正常工作

### 异常情况处理
1. **权限不足**：没有权限时按钮不显示或显示为禁用状态
2. **参数错误**：参数传递失败时降级到通用模式，显示体系选择器
3. **数据加载失败**：有友好的错误提示和降级处理

## 调试信息

### 浏览器控制台
打开浏览器开发者工具，在控制台中可以看到：
```
初始化参数: {systemId: 1, systemName: "测试体系"}
```

### 网络请求
检查以下API调用是否正常：
1. `POST /system/system/list` - 获取体系列表
2. `GET /system/architecture/personnel/tree/{systemId}` - 获取体系结构树
3. `GET /system/architecture/personnel/orgInfo/{architectureId}` - 获取机构信息
4. `POST /system/architecture/personnel/staffList` - 获取人员列表

## 常见问题

### 1. 人员维护按钮不显示
- 检查用户是否有 `system:architecture:personnel:view` 权限
- 确认菜单配置SQL已正确执行

### 2. 点击按钮没有反应
- 检查浏览器控制台是否有JavaScript错误
- 确认 `openPersonnelManagement` 函数是否正确定义

### 3. 页面打开但体系没有自动选择
- 检查URL参数是否正确传递
- 查看浏览器控制台的调试信息
- 确认体系列表是否正常加载

### 4. 体系结构树不显示
- 检查体系ID是否存在
- 确认体系结构数据是否正确
- 查看网络请求是否成功

## 后续优化建议

1. **性能优化**：可以考虑缓存体系列表数据
2. **用户体验**：添加加载动画和更好的错误提示
3. **功能扩展**：支持批量人员操作
4. **权限细化**：可以针对不同体系设置不同的权限
