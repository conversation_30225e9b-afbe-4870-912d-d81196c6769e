# 人员列表显示问题排查说明

## 问题描述

左侧树可以正常显示，点击树节点后右侧上面区域正常显示了机构信息，但下面的机构人员未能正常显示列表及数据。

## 问题分析

通过代码分析，可能的问题点包括：

1. **树节点数据结构问题**：树节点可能没有正确的机构编码字段
2. **表格初始化问题**：表格可能没有正确初始化或配置
3. **数据传递问题**：机构编码可能没有正确传递到后端
4. **后端查询问题**：后端可能没有正确处理查询参数

## 修复内容

### 1. 增强树节点点击事件的调试信息

#### 修改前
```javascript
function onTreeNodeClick(event, treeId, treeNode) {
    currentArchitectureId = treeNode.id;
    loadOrgInfo(treeNode.id);
    
    if (treeNode.type === 'organization') {
        currentOrgCode = treeNode.orgCode;
        $('#currentOrgCode').val(currentOrgCode);
        showStaffList();
        refreshStaffTable();
    } else {
        hideStaffList();
    }
}
```

#### 修改后
```javascript
function onTreeNodeClick(event, treeId, treeNode) {
    console.log('点击树节点:', treeNode);
    console.log('节点类型:', treeNode.type);
    console.log('机构编码:', treeNode.orgCode || treeNode.relatedCode);
    
    currentArchitectureId = treeNode.id;
    loadOrgInfo(treeNode.id);

    // 检查是否是机构节点（可能使用不同的字段名）
    let orgCode = treeNode.orgCode || treeNode.relatedCode;
    if (treeNode.type === 'organization' || orgCode) {
        currentOrgCode = orgCode;
        console.log('设置当前机构编码:', currentOrgCode);
        $('#currentOrgCode').val(currentOrgCode);
        showStaffList();
        refreshStaffTable();
    } else {
        console.log('非机构节点，隐藏人员列表');
        hideStaffList();
    }
}
```

### 2. 修改后端控制器以支持完整的查询对象

#### 修改前
```java
@PostMapping("/staffList")
@ResponseBody
public TableDataInfo getStaffList(String orgCode) {
    // 只接收orgCode字符串参数
}
```

#### 修改后
```java
@PostMapping("/staffList")
@ResponseBody
public TableDataInfo getStaffList(IdwOrgStaff staff) {
    try {
        String orgCode = staff.getOrgCode();
        if (orgCode != null) {
            orgCode = URLDecoder.decode(orgCode, "utf-8");
            staff.setOrgCode(orgCode);
        }
        
        logger.info("查询人员列表，机构编码: {}", orgCode);
        
        startPage();
        List<IdwOrgStaff> list = idwOrgStaffService.selectIdwOrgStaffList(staff);
        
        logger.info("查询到人员数量: {}", list != null ? list.size() : 0);
        
        return getDataTable(list);
    } catch (UnsupportedEncodingException e) {
        logger.error("查询人员列表失败", e);
        return getDataTable(null);
    }
}
```

### 3. 优化表格刷新方法

#### 修改前
```javascript
function refreshStaffTable() {
    if (currentOrgCode) {
        $('#bootstrap-table-staff').bootstrapTable('refresh');
    }
}
```

#### 修改后
```javascript
function refreshStaffTable() {
    if (currentOrgCode) {
        console.log('刷新人员列表，机构编码:', currentOrgCode);
        // 更新隐藏字段
        $('#currentOrgCode').val(currentOrgCode);
        // 使用$.table.search方法刷新表格
        $.table.search('form-staff', 'bootstrap-table-staff');
    } else {
        console.log('无机构编码，无法刷新人员列表');
    }
}
```

### 4. 添加显示人员列表的调试信息

```javascript
function showStaffList() {
    console.log('显示人员列表区域');
    $('#noStaffSelection').hide();
    $('#staffListContent').show();
}
```

## 排查步骤

### 1. 检查浏览器控制台

打开浏览器开发者工具，查看控制台输出：

#### 正常情况应该看到：
```
点击树节点: {id: "123", name: "测试机构", type: "organization", orgCode: "ORG001", ...}
节点类型: organization
机构编码: ORG001
设置当前机构编码: ORG001
显示人员列表区域
刷新人员列表，机构编码: ORG001
```

#### 异常情况可能看到：
```
点击树节点: {id: "123", name: "测试分类", type: "category", ...}
节点类型: category
机构编码: undefined
非机构节点，隐藏人员列表
```

### 2. 检查网络请求

在浏览器开发者工具的Network标签中，查看是否有以下请求：

1. **机构信息请求**：
   - URL: `GET /system/architecture/personnel/orgInfo/123`
   - 状态: 200
   - 响应: 包含机构信息的JSON

2. **人员列表请求**：
   - URL: `POST /system/architecture/personnel/staffList`
   - 状态: 200
   - 请求体: 包含 `orgCode` 参数
   - 响应: 包含人员列表的JSON

### 3. 检查树节点数据结构

在控制台中查看树节点的完整数据结构：

```javascript
// 在onTreeNodeClick函数中添加
console.log('完整节点数据:', JSON.stringify(treeNode, null, 2));
```

确认节点是否包含以下字段：
- `id`: 节点ID
- `type`: 节点类型（应该是 "organization" 或其他）
- `orgCode` 或 `relatedCode`: 机构编码

### 4. 检查后端日志

查看应用服务器日志，确认：

1. **人员列表查询日志**：
   ```
   INFO - 查询人员列表，机构编码: ORG001
   INFO - 查询到人员数量: 5
   ```

2. **SQL执行日志**（如果开启了SQL日志）：
   ```sql
   SELECT * FROM idw_org_staff WHERE org_code = 'ORG001'
   ```

## 常见问题及解决方案

### 1. 树节点没有机构编码

**现象**：控制台显示 `机构编码: undefined`

**原因**：树节点数据中没有 `orgCode` 或 `relatedCode` 字段

**解决方案**：
- 检查后端返回的树数据结构
- 确认数据库中的体系结构表是否正确关联了机构编码
- 修改树节点点击事件，使用正确的字段名

### 2. 人员列表请求没有发送

**现象**：Network中没有看到 `/staffList` 请求

**原因**：
- 机构编码为空，导致不满足刷新条件
- 表格初始化失败

**解决方案**：
- 确保 `currentOrgCode` 有值
- 检查表格是否正确初始化
- 检查表单ID和表格ID是否正确

### 3. 后端返回空数据

**现象**：请求发送成功，但返回的数据为空

**原因**：
- 数据库中没有对应机构的人员数据
- 机构编码不匹配
- 查询条件有误

**解决方案**：
- 检查数据库中是否有测试数据
- 确认机构编码的格式和值是否正确
- 检查后端查询逻辑

### 4. 表格显示异常

**现象**：有数据返回，但表格显示异常

**原因**：
- 表格列配置有误
- 数据字段名不匹配
- 权限问题

**解决方案**：
- 检查表格列的 `field` 配置
- 确认返回数据的字段名
- 检查用户权限

## 测试数据准备

为了测试人员列表功能，需要准备以下测试数据：

### 1. 体系结构数据
```sql
INSERT INTO idw_org_system_architecture (id, system_id, parent_id, name, type, org_code, ancestors, order_num, is_delete)
VALUES (1, 1, 0, '测试机构', 'organization', 'TEST_ORG_001', '0', 1, 0);
```

### 2. 机构人员数据
```sql
INSERT INTO idw_org_staff (staff_id, org_code, people_name_cn, people_name_en, position, status, create_time, update_time)
VALUES 
(1, 'TEST_ORG_001', '张三', 'Zhang San', '经理', 'current', NOW(), NOW()),
(2, 'TEST_ORG_001', '李四', 'Li Si', '主管', 'current', NOW(), NOW());
```

## 验证步骤

1. **点击树节点**：选择一个机构类型的节点
2. **查看控制台**：确认调试信息正确输出
3. **检查网络请求**：确认人员列表请求正常发送
4. **验证数据显示**：确认人员列表正确显示

通过以上排查步骤，应该能够定位并解决人员列表显示的问题。
