# 指定体系人员维护功能测试说明

## 功能概述

优化了体系人员维护功能，当从体系管理页面点击"人员维护"按钮时，会传递当前体系的ID到人员维护界面，左侧树直接加载对应的体系结构树，无需用户再次选择体系。

## 主要改进

### 1. 界面模式区分

#### 通用模式（无指定体系）
- 显示体系选择下拉框
- 用户需要手动选择体系
- 适用于直接访问人员维护页面的场景

#### 指定体系模式（有指定体系）
- 隐藏体系选择下拉框
- 显示当前体系信息
- 直接加载指定体系的结构树
- 适用于从体系管理页面跳转的场景

### 2. 界面元素变化

#### 左侧面板结构
```html
<!-- 体系选择器（通用模式） -->
<div class="system-selector" id="systemSelectorDiv">
    <select id="systemSelect" class="form-control">
        <option value="">请选择体系</option>
    </select>
</div>

<!-- 体系信息显示（指定体系模式） -->
<div class="system-info" id="systemInfoDiv" style="display: none;">
    <div class="system-info-item">
        <span class="system-info-label">当前体系：</span>
        <span class="system-info-value" id="currentSystemName"></span>
    </div>
</div>
```

#### 样式优化
- 体系信息显示区域采用浅灰色背景
- 体系名称使用绿色高亮显示
- 页面标题动态更新为"[体系名称] - 体系结构"

## 技术实现

### 1. 参数传递流程

```
体系管理页面 → 点击"人员维护" → 传递systemId和systemName → 控制器接收 → 页面渲染
```

### 2. 前端初始化逻辑

```javascript
$(function() {
    // 根据是否有指定体系ID来决定显示模式
    if (initialSystemId) {
        // 指定体系模式：隐藏选择器，显示体系信息，直接加载体系结构树
        initSpecificSystemMode();
    } else {
        // 通用模式：显示体系选择器
        initGeneralMode();
    }
});
```

### 3. 模式切换函数

#### 指定体系模式初始化
```javascript
function initSpecificSystemMode() {
    // 隐藏体系选择器，显示体系信息
    $('#systemSelectorDiv').hide();
    $('#systemInfoDiv').show();
    $('#currentSystemName').text(initialSystemName || '未知体系');
    
    // 更新页面标题
    $('.tree-title').text((initialSystemName || '未知体系') + ' - 体系结构');
    
    // 直接加载指定体系的结构树
    loadArchitectureTree(initialSystemId);
}
```

#### 通用模式初始化
```javascript
function initGeneralMode() {
    // 显示体系选择器，隐藏体系信息
    $('#systemSelectorDiv').show();
    $('#systemInfoDiv').hide();
    
    // 加载体系列表
    loadSystemList();
}
```

## 测试步骤

### 1. 测试指定体系模式

#### 步骤
1. 登录系统，访问体系管理页面
2. 在体系列表中找到任意一个体系
3. 点击该体系的"人员维护"按钮
4. 观察新打开的页签

#### 预期结果
- 新页签标题为"[体系名称] - 人员维护"
- 左侧不显示体系选择下拉框
- 左侧显示"当前体系：[体系名称]"信息
- 页面标题显示为"[体系名称] - 体系结构"
- 体系结构树自动加载对应体系的数据
- 可以正常点击树节点查看机构信息和人员列表

### 2. 测试通用模式

#### 步骤
1. 直接访问人员维护页面URL：`/system/architecture/personnel`
2. 观察页面显示

#### 预期结果
- 左侧显示体系选择下拉框
- 不显示体系信息区域
- 页面标题显示为"体系结构"
- 需要手动选择体系才能加载结构树

### 3. 测试功能完整性

#### 步骤
1. 通过体系管理页面进入指定体系的人员维护界面
2. 点击体系结构树中的机构节点
3. 查看机构信息是否正确显示
4. 查看人员列表是否正确加载
5. 测试添加、编辑、删除人员功能

#### 预期结果
- 所有功能正常工作
- 数据显示正确
- 操作响应正常

## 调试信息

### 浏览器控制台输出
```
初始化参数: {systemId: 1, systemName: "测试体系"}
初始化指定体系模式
加载体系结构树，systemId: 1
体系结构树数据: [...]
点击树节点: {...}
```

### 网络请求检查
1. `GET /system/architecture/personnel?systemId=1&systemName=测试体系`
2. `GET /system/architecture/personnel/tree/1`
3. `GET /system/architecture/personnel/orgInfo/{architectureId}`
4. `POST /system/architecture/personnel/staffList`

## 错误处理

### 1. 体系ID无效
- 显示错误提示
- 降级到通用模式

### 2. 体系结构树加载失败
- 显示友好的错误信息
- 提供重试机制

### 3. 网络连接问题
- 显示网络错误提示
- 建议检查网络连接

## 兼容性说明

### 向后兼容
- 原有的通用模式功能完全保留
- 直接访问人员维护页面的用户体验不变
- 所有现有功能正常工作

### URL参数支持
- `systemId`: 体系ID（可选）
- `systemName`: 体系名称（可选，用于显示）

## 优化建议

### 1. 性能优化
- 可以考虑缓存体系结构树数据
- 优化大型体系结构树的渲染性能

### 2. 用户体验
- 添加加载动画
- 优化错误提示信息
- 支持键盘快捷键操作

### 3. 功能扩展
- 支持体系切换功能（在指定体系模式下）
- 添加面包屑导航
- 支持收藏常用体系
