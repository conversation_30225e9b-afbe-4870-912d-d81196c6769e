package com.lirong.system.architecture.controller;

import com.lirong.common.annotation.Log;
import com.lirong.common.core.controller.BaseController;
import com.lirong.common.core.domain.AjaxResult;
import com.lirong.common.core.domain.Ztree;
import com.lirong.common.core.page.TableDataInfo;
import com.lirong.common.enums.BusinessType;
import com.lirong.common.utils.StringUtils;
import com.lirong.organization.common.domain.IdwOrg;
import com.lirong.organization.common.service.IdwOrgService;
import com.lirong.organization.staff.domain.IdwOrgStaff;
import com.lirong.organization.staff.service.IdwOrgStaffService;
import com.lirong.personnel.common.domain.IdwPeopleMain;
import com.lirong.personnel.common.service.IdwPeopleMainService;
import com.lirong.system.architecture.domain.IdwOrgSystemArchitecture;
import com.lirong.system.architecture.service.IdwOrgSystemArchitectureService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.List;
import java.util.Map;

/**
 * 体系结构人员管理Controller
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
@Controller
@RequestMapping("/system/architecture/personnel")
public class IdwOrgSystemArchitecturePersonnelController extends BaseController {
    private String prefix = "system/architecture/personnel";

    @Autowired
    private IdwOrgSystemArchitectureService idwOrgSystemArchitectureService;
    
    @Autowired
    private IdwOrgService idwOrgService;
    
    @Autowired
    private IdwOrgStaffService idwOrgStaffService;
    
    @Autowired
    private IdwPeopleMainService idwPeopleMainService;

    /**
     * 跳转体系结构人员管理主页面
     */
    @RequiresPermissions("system:architecture:personnel:view")
    @GetMapping()
    public String index(@RequestParam(value = "systemId", required = false) Long systemId,
                       @RequestParam(value = "systemName", required = false) String systemName,
                       ModelMap mmap) {
        // 将参数传递给页面，用于自动选择体系
        if (systemId != null) {
            mmap.put("systemId", systemId.toString());
        }
        if (systemName != null) {
            mmap.put("systemName", systemName);
        }
        return prefix + "/systemArchitecturePersonnel";
    }

    /**
     * 构建体系结构树
     */
    @GetMapping("/tree/{systemId}")
    @ResponseBody
    public List<Ztree> buildArchitectureTree(@PathVariable("systemId") Long systemId) {
        return idwOrgSystemArchitectureService.architectureTree(systemId, null);
    }

    /**
     * 根据体系结构ID获取机构信息
     */
    @GetMapping("/orgInfo/{architectureId}")
    @ResponseBody
    public AjaxResult getOrgInfo(@PathVariable("architectureId") Long architectureId) {
        try {
            IdwOrgSystemArchitecture architecture = idwOrgSystemArchitectureService.selectOrgSystemArchitectureById(architectureId);
            if (architecture != null && "organization".equals(architecture.getType())) {
                IdwOrg org = idwOrgService.selectOrgByOrgCode(architecture.getOrgCode());
                return AjaxResult.success(org);
            }
            return AjaxResult.success(architecture);
        } catch (Exception e) {
            return AjaxResult.error("获取机构信息失败");
        }
    }

    /**
     * 根据机构编码查询人员列表
     */
    @PostMapping("/staffList")
    @ResponseBody
    public TableDataInfo getStaffList(IdwOrgStaff staff) {
        try {
            String orgCode = staff.getOrgCode();
            if (orgCode != null) {
                orgCode = URLDecoder.decode(orgCode, "utf-8");
                staff.setOrgCode(orgCode);
            }

            logger.info("查询人员列表，机构编码: {}", orgCode);

            startPage();
            List<IdwOrgStaff> list = idwOrgStaffService.selectIdwOrgStaffList(staff);

            logger.info("查询到人员数量: {}", list != null ? list.size() : 0);

            return getDataTable(list);
        } catch (UnsupportedEncodingException e) {
            logger.error("查询人员列表失败", e);
            return getDataTable(null);
        }
    }

    /**
     * 跳转添加人员页面
     */
    @GetMapping("/addStaff/{orgCode}")
    public String addStaff(@PathVariable("orgCode") String orgCode, ModelMap mmap) {
        try {
            orgCode = URLDecoder.decode(orgCode, "utf-8");
            mmap.put("orgCode", orgCode);
            return prefix + "/addStaff";
        } catch (UnsupportedEncodingException e) {
            logger.error("跳转添加人员页面失败", e);
            return prefix + "/addStaff";
        }
    }

    /**
     * 跳转编辑人员页面
     */
    @GetMapping("/editStaff/{staffId}")
    public String editStaff(@PathVariable("staffId") Long staffId, ModelMap mmap) {
        IdwOrgStaff staff = idwOrgStaffService.selectIdwOrgStaffById(staffId);
        mmap.put("staff", staff);

        // 如果有关联的人员详细信息，也加载进来
        if (staff != null && staff.getPeopleCode() != null) {
            IdwPeopleMain peopleMain = idwPeopleMainService.selectPeopleByPeopleCode(staff.getPeopleCode());
            mmap.put("peopleMain", peopleMain);
        }

        return prefix + "/editStaff";
    }

    /**
     * 新增保存人员信息（融合机构人员和详细人员信息）
     */
    @RequiresPermissions("system:architecture:personnel:add")
    @Log(title = "体系结构人员管理", businessType = BusinessType.INSERT)
    @PostMapping("/addStaff")
    @ResponseBody
    public AjaxResult addStaffSave(IdwOrgStaff staff, HttpServletRequest request) {
        try {
            logger.info("开始保存人员信息，机构人员：{}", staff);

            // 从请求中获取详细人员信息的参数
            IdwPeopleMain peopleMain = extractPeopleMainFromRequest(request);
            logger.info("提取的详细人员信息：{}", peopleMain);

            // 先保存详细人员信息（如果有的话）
            if (peopleMain != null && StringUtils.isNotEmpty(peopleMain.getNameCn())) {
                String peopleCode = peopleMain.getPeopleCode();
                if (StringUtils.isEmpty(peopleCode)) {
                    String maxPeopleCode = idwPeopleMainService.selectMaxPeopleCode();
                    String newPeopleCodeNumber = "0000000" + String.valueOf(Long.parseLong(maxPeopleCode.substring(1)) + 1);
                    peopleCode = maxPeopleCode.charAt(0) + newPeopleCodeNumber.substring(newPeopleCodeNumber.length() - 7);
                }

                peopleMain.setStatus("current"); // 设置默认状态
                peopleMain.setIsDelete(0);


                logger.info("保存详细人员信息，人员编码：{}", peopleCode);
                int result = idwPeopleMainService.insertIdwPeopleMain(peopleMain);
                System.out.println(result);

                // 关联到机构人员
                staff.setPeopleCode(peopleCode);
            }

            // 保存机构人员信息
            logger.info("保存机构人员信息：{}", staff);
            return toAjax(idwOrgStaffService.insertIdwOrgStaff(staff));
        } catch (Exception e) {
            logger.error("新增人员失败", e);
            return AjaxResult.error("新增人员失败：" + e.getMessage());
        }
    }

    /**
     * 修改保存人员信息（融合机构人员和详细人员信息）
     */
    @RequiresPermissions("system:architecture:personnel:edit")
    @Log(title = "体系结构人员管理", businessType = BusinessType.UPDATE)
    @PostMapping("/editStaff")
    @ResponseBody
    public AjaxResult editStaffSave(IdwOrgStaff staff, HttpServletRequest request) {
        try {
            logger.info("开始修改人员信息，机构人员：{}", staff);

            // 从请求中获取详细人员信息的参数
            IdwPeopleMain peopleMain = extractPeopleMainFromRequest(request);
            logger.info("提取的详细人员信息：{}", peopleMain);

            // 更新详细人员信息（如果有的话）
            if (peopleMain != null && staff.getPeopleCode() != null) {
                peopleMain.setPeopleCode(staff.getPeopleCode());
                logger.info("更新详细人员信息，人员编码：{}", staff.getPeopleCode());
                idwPeopleMainService.updatePeople(peopleMain);
            }

            // 更新机构人员信息
            logger.info("更新机构人员信息：{}", staff);
            return toAjax(idwOrgStaffService.updateIdwOrgStaff(staff));
        } catch (Exception e) {
            logger.error("修改人员失败", e);
            return AjaxResult.error("修改人员失败：" + e.getMessage());
        }
    }

    /**
     * 删除人员
     */
    @RequiresPermissions("system:architecture:personnel:remove")
    @Log(title = "体系结构人员管理", businessType = BusinessType.DELETE)
    @PostMapping("/removeStaff")
    @ResponseBody
    public AjaxResult removeStaff(String ids) {
        try {
            return toAjax(idwOrgStaffService.deleteIdwOrgStaffByIds(ids));
        } catch (Exception e) {
            logger.error("删除人员失败", e);
            return AjaxResult.error("删除人员失败");
        }
    }

    /**
     * 获取最大人员编码
     */
    @PostMapping("/selectMaxPeopleCode")
    @ResponseBody
    public String selectMaxPeopleCode() {
        String maxPeopleCode = idwPeopleMainService.selectMaxPeopleCode();
        String newPeopleCodeNumber = "0000000" + String.valueOf(Long.parseLong(maxPeopleCode.substring(1)) + 1);
        return maxPeopleCode.charAt(0) + newPeopleCodeNumber.substring(newPeopleCodeNumber.length() - 7);
    }

    /**
     * 获取最大排序号
     */
    @PostMapping("/selectMaxOrderNum")
    @ResponseBody
    public Integer selectMaxOrderNum(String category) {
        return idwPeopleMainService.selectMaxOrderNum(category) + 1;
    }

    /**
     * 校验人员编码唯一性
     */
    @GetMapping("/checkoutPeopleCode/{peopleCode}")
    @ResponseBody
    public boolean checkoutPeopleCode(@PathVariable("peopleCode") String peopleCode) {
        IdwPeopleMain idwPeopleMain = idwPeopleMainService.selectPeopleByPeopleCode(peopleCode);
        return idwPeopleMain == null;
    }

    /**
     * AI解析英文简介
     */
    @PostMapping("/parseEnglishBio")
    @ResponseBody
    public AjaxResult parseEnglishBio(String profileEn) {
        try {
            if (StringUtils.isBlank(profileEn)) {
                return AjaxResult.error("英文简介内容不能为空");
            }

            Map<String, Object> parsedData = idwPeopleMainService.parseEnglishBiography(profileEn);
            return AjaxResult.success("AI解析成功", parsedData);
        } catch (Exception e) {
            logger.error("AI解析失败", e);
            return AjaxResult.error("AI解析失败：" + e.getMessage());
        }
    }

    /**
     * 从请求中提取IdwPeopleMain对象的参数
     */
    private IdwPeopleMain extractPeopleMainFromRequest(HttpServletRequest request) {
        IdwPeopleMain peopleMain = new IdwPeopleMain();

        String orgCode = request.getParameter("orgCode");
        String orgName = request.getParameter("orgName");

        String peopleCode = request.getParameter("peopleCode");


        // 基本信息
        String peopleNameCn = request.getParameter("peopleNameCn");
        String peopleNameEn = request.getParameter("peopleNameEn");
        String country = request.getParameter("country");
        String gender = request.getParameter("gender");
        String peopleTypeDetail = request.getParameter("peopleTypeDetail");

        // 详细信息
        String birthdayYear = request.getParameter("birthdayYear");
        String birthdayMonth = request.getParameter("birthdayMonth");
        String birthdayDay = request.getParameter("birthdayDay");
        String birthplace = request.getParameter("birthplace");
        String age = request.getParameter("age");
        String email = request.getParameter("email");
        String telephone = request.getParameter("telephone");
        String troopsCategory = request.getParameter("troopsCategory");
        String militaryRank = request.getParameter("militaryRank");
        String graduatedUniversity = request.getParameter("graduatedUniversity");
        String education = request.getParameter("education");
        String post = request.getParameter("post");
        String appointmentYear = request.getParameter("appointmentYear");
        String appointmentMonth = request.getParameter("appointmentMonth");
        String appointmentDay = request.getParameter("appointmentDay");
        String party = request.getParameter("party");
        String occupation = request.getParameter("occupation");

        // 简介信息
        String profileEn = request.getParameter("profileEn");
        String profileCn = request.getParameter("profileCn");
        String source = request.getParameter("source");

        // 经历信息
        String educationalExperience = request.getParameter("educationalExperience");
        String assignments = request.getParameter("assignments");

        // 头像
        String avatar = request.getParameter("avatar");


        // 设置字段值（只有非空值才设置）
        if (StringUtils.isNotBlank(occupation)) {
            peopleMain.setOccupation(occupation);
        }
        if (StringUtils.isNotBlank(peopleCode)) {
            peopleMain.setPeopleCode(peopleCode);
        }
        if (StringUtils.isNotEmpty(peopleNameCn)) {
            peopleMain.setNameCn(peopleNameCn);
        }
        if (StringUtils.isNotEmpty(peopleNameEn)) {
            peopleMain.setNameEn(peopleNameEn);
        }
        if (StringUtils.isNotEmpty(country)) {
            peopleMain.setCountry(country);
        }
        if (StringUtils.isNotEmpty(gender)) {
            peopleMain.setGender(gender);
        }

        if (StringUtils.isNotBlank(party)) {
            peopleMain.setParty(party);
        }
        if (StringUtils.isNotEmpty(peopleTypeDetail)) {
            peopleMain.setPeopleType(peopleTypeDetail);
        }

        if (StringUtils.isNotBlank(birthplace)) {
            peopleMain.setBirthplace(birthplace);
        }

        // 处理出生日期
        if (StringUtils.isNotEmpty(birthdayYear) && StringUtils.isNotEmpty(birthdayMonth) && StringUtils.isNotEmpty(birthdayDay)) {
            String birthday = birthdayYear + "-" + String.format("%02d", Integer.parseInt(birthdayMonth)) + "-" + String.format("%02d", Integer.parseInt(birthdayDay));
            peopleMain.setBirthday(birthday);
            peopleMain.setBirthdayYear(birthdayYear);
            peopleMain.setBirthdayMonth(birthdayMonth);
            peopleMain.setBirthdayDay(birthdayDay);
        }

        if (StringUtils.isNotEmpty(age)) {
            try {
                peopleMain.setAge(Integer.parseInt(age));
            } catch (NumberFormatException e) {
                // 忽略年龄解析错误
            }
        }

        if (StringUtils.isNotEmpty(email)) {
            peopleMain.setEmail(email);
        }
        if (StringUtils.isNotEmpty(telephone)) {
            peopleMain.setTelephone(telephone);
        }
        if (StringUtils.isNotEmpty(troopsCategory)) {
            peopleMain.setTroopsCategory(troopsCategory);
        }
        if (StringUtils.isNotEmpty(militaryRank)) {
            peopleMain.setMilitaryRank(militaryRank);
        }
        if (StringUtils.isNotEmpty(graduatedUniversity)) {
            peopleMain.setGraduatedUniversity(graduatedUniversity);
        }
        if (StringUtils.isNotEmpty(education)) {
            peopleMain.setEducation(education);
        }
        if (StringUtils.isNotBlank(orgCode)) {
            peopleMain.setOrgCode(orgCode);
        }
        if (StringUtils.isNotEmpty(orgName)) {
            peopleMain.setOrgName(orgName);
        }
        if (StringUtils.isNotEmpty(post)) {
            peopleMain.setPost(post);
        }

        // 处理任职日期
        if (StringUtils.isNotEmpty(appointmentYear) && StringUtils.isNotEmpty(appointmentMonth) && StringUtils.isNotEmpty(appointmentDay)) {
            String appointmentDate = appointmentYear + "-" + String.format("%02d", Integer.parseInt(appointmentMonth)) + "-" + String.format("%02d", Integer.parseInt(appointmentDay));
            peopleMain.setAppointmentDate(appointmentDate);
            peopleMain.setAppointmentYear(appointmentYear);
            peopleMain.setAppointmentMonth(appointmentMonth);
            peopleMain.setAppointmentDay(appointmentDay);
        }

        if (StringUtils.isNotEmpty(profileEn)) {
            peopleMain.setProfileEn(profileEn);
        }
        if (StringUtils.isNotEmpty(profileCn)) {
            peopleMain.setProfileCn(profileCn);
        }
        if (StringUtils.isNotEmpty(source)) {
            peopleMain.setSource(source);
        }
        if (StringUtils.isNotEmpty(educationalExperience)) {
            peopleMain.setEducationalExperience(educationalExperience);
        }
        if (StringUtils.isNotEmpty(assignments)) {
            peopleMain.setAssignments(assignments);
        }
        if (StringUtils.isNotEmpty(avatar)) {
            peopleMain.setAvatar(avatar);
        }

        return peopleMain;
    }
}
