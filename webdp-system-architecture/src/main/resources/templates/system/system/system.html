<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('体系列表')" />
    <th:block th:include="include :: select2-css" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="form-system">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label style="width: 100px;">国家/地区：</label>
                                <select class="form-control" name="country" th:with="type=${@dict.getType('sys_country')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <label>名称：</label>
                                <input type="text" name="name"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search('form-system', 'bootstrap-table-system')"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset('form-system', 'bootstrap-table-system')"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar-system" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="system.system:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="system.system:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table-system"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
     <th:block th:include="include :: select2-js" />
    <script th:inline="javascript">
        let editFlag = [[${@permission.hasPermi('system.system:edit')}]];
        let removeFlag = [[${@permission.hasPermi('system.system:remove')}]];
        let personnelFlag = [[${@permission.hasPermi('system:architecture:personnel:view')}]];
        let countryDatas = [[${@dict.getType('sys_country')}]]
        let prefix = ctx + "system/system";
        let architecture = ctx + "system/architecture";

        $(function() {
            let options = {
                id: "bootstrap-table-system",          // 指定表格ID
                toolbar: "toolbar-system",   // 指定工具栏ID
                formId: "form-system",
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "体系",
                uniqueId: "systemId",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'index',
                    align: 'center',
                    title: "序号",
                    formatter: function (value, row, index) {
                        var columnIndex = $.common.sprintf("<input type='hidden' name='index' value='%s'>", $.table.serialNumber(index));
                        return columnIndex + $.table.serialNumber(index);
                    }
                },
                {
                    align: 'left',
                    field: 'name',
                    title: '名称',
                    formatter: function(value, row, index) {
                        return '<a style="margin-left: 10px;" href="javascript:void(0)" onclick="openArchitecture(\'' + row.systemId + '\',\'' + row.name + '\')">' + value + '</a>';
                    }
                },
                {
                    field: 'country',
                    title: '国家/地区',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(countryDatas, value);
                    }
                },
                {
                    width: '15',
                    field: 'type',
                    title: '类型'
                },
                {
                    field: 'remark',
                    title: '备注'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        let actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.systemId + '\')"><i class="fa fa-edit"></i>修改</a> ');
                        actions.push('<a class="btn btn-primary btn-xs ' + personnelFlag + '" href="javascript:void(0)" onclick="openPersonnelManagement(\'' + row.systemId + '\',\'' + row.name + '\')" title="体系结构人员维护"><i class="fa fa-users"></i>人员维护</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="remove(\'' + row.systemId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        function openArchitecture(id, name){
            let systemName = encodeURI(encodeURI(name))
            $.modal.openTab(name + '体系', architecture + "/" + systemName + "/" + id);
        }

        function openPersonnelManagement(systemId, systemName) {
            // 打开体系结构人员管理页面，并传递体系ID参数
            let personnelUrl = ctx + "system/architecture/personnel?systemId=" + systemId + "&systemName=" + encodeURIComponent(systemName);
            $.modal.openTab(systemName + ' - 人员维护', personnelUrl);
        }

        function remove(systemId) {
            $.modal.confirm("确定删除该条体系信息吗？", function() {
                $.ajax({
                    type: "GET",
                    url: prefix + "/remove/" + systemId,
                    async: false,
                    success: function(result) {
                        if (result.code == web_status.SUCCESS) {
                            $.modal.msgSuccess('删除成功！')
                            $.table.search('form-system', 'bootstrap-table-system')
                        }
                    }
                });
            });
        }
    </script>
</body>
</html>