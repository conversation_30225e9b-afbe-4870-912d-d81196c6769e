<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('体系结构人员管理')" />
    <th:block th:include="include :: ztree-css" />
    <style type="text/css">
        .main-content {
            height: calc(100vh - 40px);
            overflow: hidden;
        }
        .left-panel {
            width: 400px;
            height: 100%;
            border-right: 1px solid #e7eaec;
            overflow-y: auto;
            background: #fff;
            padding: 15px;
        }
        .right-panel {
            flex: 1;
            height: 100%;
            display: flex;
            flex-direction: column;
            background: #fff;
        }
        .org-info-panel {
            height: 220px;
            border-bottom: 1px solid #e7eaec;
            padding: 15px;
            overflow-y: auto;
        }
        .staff-list-panel {
            flex: 1;
            padding: 15px;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }
        .staff-list-content {
            flex: 1;
            overflow-y: auto;
            min-height: 0;
        }
        .staff-list-content {
            flex: 1;
            overflow-y: auto;
            min-height: 0;
        }
        .tree-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        .org-info-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            border-bottom: 2px solid #1ab394;
            padding-bottom: 5px;
        }
        .staff-list-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            border-bottom: 2px solid #1ab394;
            padding-bottom: 5px;
        }
        .org-info-content {
            display: none;
        }
        .org-info-item {
            margin-bottom: 10px;
        }
        .org-info-label {
            font-weight: bold;
            color: #666;
            display: inline-block;
            width: 100px;
        }
        .org-info-value {
            color: #333;
        }
        .no-selection {
            text-align: center;
            color: #999;
            padding: 50px 0;
        }
        .system-selector {
            margin-bottom: 15px;
        }
        .system-selector select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .system-info {
            margin-bottom: 15px;
            padding: 10px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
        }
        .system-info-item {
            margin-bottom: 5px;
        }
        .system-info-label {
            font-weight: bold;
            color: #495057;
            display: inline-block;
            width: 80px;
        }
        .system-info-value {
            color: #1ab394;
            font-weight: bold;
        }
        .treeExpandCollapse {
            font-size: 12px;
            color: #666;
        }
        .treeExpandCollapse a {
            color: #1ab394;
            text-decoration: none;
        }
        .treeExpandCollapse a:hover {
            color: #0e8c73;
            text-decoration: underline;
        }
        .d-flex {
            display: flex;
        }
    </style>
</head>
<body class="gray-bg">
    <div class="wrapper wrapper-content">
        <div class="main-content d-flex">
            <!-- 左侧体系结构树 -->
            <div class="left-panel">
                <div class="tree-title">体系结构</div>

                <!-- 体系选择器（通用模式） -->
                <div class="system-selector" id="systemSelectorDiv">
                    <select id="systemSelect" class="form-control">
                        <option value="">请选择体系</option>
                    </select>
                </div>

                <!-- 体系信息显示（指定体系模式） -->
                <div class="system-info" id="systemInfoDiv" style="display: none;">
                    <div class="system-info-item">
                        <span class="system-info-label">当前体系：</span>
                        <span class="system-info-value" id="currentSystemName"></span>
                    </div>
                </div>

                <!-- 树操作按钮 -->
                <div class="treeExpandCollapse" id="treeExpandCollapse" style="display: none; margin-bottom: 10px;">
                    <a href="#" onclick="$.tree.expand()">展开</a> /
                    <a href="#" onclick="$.tree.collapse()">折叠</a>
                </div>

                <div id="tree" class="ztree treeselect"></div>
            </div>
            
            <!-- 右侧内容区域 -->
            <div class="right-panel">
                <!-- 机构信息展示区域 -->
                <div class="org-info-panel">
                    <div class="org-info-title">机构信息</div>
                    <div class="no-selection" id="noOrgSelection">
                        请在左侧选择体系结构节点查看机构信息
                    </div>
                    <div class="org-info-content" id="orgInfoContent">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="org-info-item">
                                    <span class="org-info-label">机构名称：</span>
                                    <span class="org-info-value" id="orgNameCn"></span>
                                </div>
                                <div class="org-info-item">
                                    <span class="org-info-label">英文名称：</span>
                                    <span class="org-info-value" id="orgNameEn"></span>
                                </div>
                                <div class="org-info-item">
                                    <span class="org-info-label">机构类型：</span>
                                    <span class="org-info-value" id="orgType"></span>
                                </div>
                                <div class="org-info-item">
                                    <span class="org-info-label">机构编码：</span>
                                    <span class="org-info-value" id="orgCode"></span>
                                </div>
                                <div class="org-info-item">
                                    <span class="org-info-label">机构官网：</span>
                                    <span class="org-info-value" id="website"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="org-info-item">
                                    <span class="org-info-label">国家/地区：</span>
                                    <span class="org-info-value" id="country"></span>
                                </div>
                                <div class="org-info-item">
                                    <span class="org-info-label">地址：</span>
                                    <span class="org-info-value" id="address"></span>
                                </div>
                                <div class="org-info-item">
                                    <span class="org-info-label">联系方式：</span>
                                    <span class="org-info-value" id="telephone"></span>
                                </div>
                                <div class="org-info-item">
                                    <span class="org-info-label">人员数量：</span>
                                    <span class="org-info-value" id="peopleCount"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 人员列表区域 -->
                <div class="staff-list-panel">
                    <div class="staff-list-title">机构人员</div>
                    <div class="no-selection" id="noStaffSelection">
                        请在左侧选择机构节点查看人员列表
                    </div>
                    <div id="staffListContent" style="display: none;" class="staff-list-content">
                        <!-- 搜索区域 -->
                        <div class="row">
                            <div class="col-sm-12 search-collapse">
                                <form id="form-staff">
                                    <input type="hidden" name="orgCode" id="currentOrgCode">
                                    <div class="select-list">
                                        <ul>
                                            <li>
                                                <label>人员名称：</label>
                                                <input type="text" name="peopleNameCn"/>
                                            </li>
                                            <li>
                                                <label>职位：</label>
                                                <input type="text" name="position"/>
                                            </li>
                                            <li>
                                                <label>状态：</label>
                                                <select name="status" th:with="type=${@dict.getType('sys_staff_status')}">
                                                    <option value="">所有</option>
                                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                                </select>
                                            </li>
                                            <li>
                                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search('form-staff', 'bootstrap-table-staff')"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset('form-staff', 'bootstrap-table-staff')"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                                            </li>
                                        </ul>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- 工具栏 -->
                        <div class="btn-group-sm" id="toolbar-staff" role="group">
                            <a class="btn btn-success" onclick="addStaff()" shiro:hasPermission="system:architecture:personnel:add">
                                <i class="fa fa-plus"></i> 添加人员
                            </a>
                            <a class="btn btn-primary single disabled" onclick="editStaff()" shiro:hasPermission="system:architecture:personnel:edit">
                                <i class="fa fa-edit"></i> 修改
                            </a>
                            <a class="btn btn-danger multiple disabled" onclick="removeStaff()" shiro:hasPermission="system:architecture:personnel:remove">
                                <i class="fa fa-remove"></i> 删除
                            </a>
                        </div>

                        <!-- 人员列表表格 -->
                        <div class="col-sm-12 select-table table-striped">
                            <table id="bootstrap-table-staff"></table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <th:block th:include="include :: footer" />
    <th:block th:include="include :: ztree-js" />
    <script th:inline="javascript">
        let prefix = ctx + "system/architecture/personnel";
        let currentOrgCode = null;
        let currentArchitectureId = null;

        // 从服务器端获取参数
        let initialSystemId = [[${systemId}]] || null;
        let initialSystemName = [[${systemName}]] || null;

        // 权限标识
        let editFlag = [[${@permission.hasPermi('system:architecture:personnel:edit')}]];
        let removeFlag = [[${@permission.hasPermi('system:architecture:personnel:remove')}]];
        let statusDatas = [[${@dict.getType('sys_staff_status')}]];

        $(function() {
            // 调试信息
            console.log('初始化参数:', {
                systemId: initialSystemId,
                systemName: initialSystemName
            });

            // 初始化人员列表表格（但不加载数据）
            initStaffTable();

            // 根据是否有指定体系ID来决定显示模式
            if (initialSystemId) {
                // 指定体系模式：隐藏选择器，显示体系信息，直接加载体系结构树
                initSpecificSystemMode();
            } else {
                // 通用模式：显示体系选择器
                initGeneralMode();
            }
        });

        // 初始化指定体系模式
        function initSpecificSystemMode() {
            console.log('初始化指定体系模式');

            // 隐藏体系选择器，显示体系信息
            $('#systemSelectorDiv').hide();
            $('#systemInfoDiv').show();
            $('#currentSystemName').text(initialSystemName || '未知体系');

            // 更新页面标题
            $('.tree-title').text((initialSystemName || '未知体系') + ' - 体系结构');

            // 直接加载指定体系的结构树
            loadArchitectureTree(initialSystemId);
        }

        // 初始化通用模式
        function initGeneralMode() {
            console.log('初始化通用模式');

            // 显示体系选择器，隐藏体系信息
            $('#systemSelectorDiv').show();
            $('#systemInfoDiv').hide();

            // 加载体系列表
            loadSystemList();
        }

        // 加载体系列表（仅在通用模式下使用）
        function loadSystemList() {
            $.post(ctx + "system/system/list", {}, function(result) {
                if (result.code == 0 && result.rows) {
                    let systemSelect = $('#systemSelect');
                    systemSelect.empty();
                    systemSelect.append('<option value="">请选择体系</option>');

                    result.rows.forEach(function(system) {
                        systemSelect.append('<option value="' + system.systemId + '">' + system.systemName + '</option>');
                    });
                }
            }).fail(function() {
                // 如果获取体系列表失败，添加一个默认选项用于测试
                let systemSelect = $('#systemSelect');
                systemSelect.empty();
                systemSelect.append('<option value="">请选择体系</option>');
                systemSelect.append('<option value="1">测试体系</option>');
            });
        }

        // 体系选择变化事件
        $('#systemSelect').change(function() {
            let systemId = $(this).val();
            if (systemId) {
                loadArchitectureTree(systemId);
            } else {
                $('#tree').empty();
                hideOrgInfo();
                hideStaffList();
            }
        });

        // 加载体系结构树
        function loadArchitectureTree(systemId) {
            console.log('加载体系结构树，systemId:', systemId);

            // 使用与architectureTree.html相同的方式初始化树
            let url = prefix + "/tree/" + systemId;
            let options = {
                url: url,
                expandLevel: 2,
                onClick: onTreeNodeClick,
                onAsyncSuccess: function(event, treeId, treeNode, msg) {
                    // 树加载成功后显示展开/折叠按钮
                    $('#treeExpandCollapse').show();
                    console.log('体系结构树加载成功');
                },
                onAsyncError: function(event, treeId, treeNode, XMLHttpRequest, textStatus, errorThrown) {
                    console.error('体系结构树加载失败:', textStatus, errorThrown);
                    $.modal.alertError('加载体系结构树失败，请检查网络连接或联系管理员');
                }
            };

            // 使用$.tree.init方法初始化树
            $.tree.init(options);
        }

        // 树节点点击事件
        function onTreeNodeClick(event, treeId, treeNode) {
            console.log('点击树节点:', treeNode);
            console.log('节点类型:', treeNode.type);
            console.log('机构编码:', treeNode.orgCode || treeNode.relatedCode);

            currentArchitectureId = treeNode.id;

            // 加载机构信息
            loadOrgInfo(treeNode.id);

            // 检查是否是机构节点（可能使用不同的字段名）
            let orgCode = treeNode.orgCode || treeNode.relatedCode;
            if (treeNode.type === 'organization' || orgCode) {
                currentOrgCode = orgCode;
                console.log('设置当前机构编码:', currentOrgCode);
                $('#currentOrgCode').val(currentOrgCode);
                showStaffList();
                refreshStaffTable();
            } else {
                console.log('非机构节点，隐藏人员列表');
                hideStaffList();
            }
        }

        // 加载机构信息
        function loadOrgInfo(architectureId) {
            $.get(prefix + "/orgInfo/" + architectureId, function(result) {
                if (result.code == 0 && result.data) {
                    showOrgInfo(result.data);
                } else {
                    hideOrgInfo();
                }
            });
        }

        // 显示机构信息
        function showOrgInfo(orgData) {
            $('#noOrgSelection').hide();
            $('#orgInfoContent').show();
            
            $('#orgNameCn').text(orgData.orgNameCn || orgData.name || '');
            $('#orgNameEn').text(orgData.orgNameEn || '');
            $('#orgType').text(orgData.orgTypeAlias || orgData.type || '');
            $('#orgCode').text(orgData.orgCode || '');
            $('#country').text(orgData.country || '');
            $('#address').text(orgData.address || '');
            $('#telephone').text(orgData.telephone || '');
            $('#peopleCount').text(orgData.peopleCount || '');
            // 支持超链接在浏览器的新页签中打开
            $('#website').html('<a href="' + orgData.officialWebsite + '" target="_blank">' + orgData.officialWebsite + '</a>');
        }

        // 隐藏机构信息
        function hideOrgInfo() {
            $('#orgInfoContent').hide();
            $('#noOrgSelection').show();
        }

        // 显示人员列表
        function showStaffList() {
            console.log('显示人员列表区域');
            $('#noStaffSelection').hide();
            $('#staffListContent').show();
        }

        // 隐藏人员列表
        function hideStaffList() {
            $('#staffListContent').hide();
            $('#noStaffSelection').show();
            currentOrgCode = null;
        }

        // 初始化人员列表表格
        function initStaffTable() {
            let options = {
                id: "bootstrap-table-staff",
                toolbar: "toolbar-staff",
                formId: "form-staff",
                url: prefix + "/staffList",
                createUrl: prefix + "/addStaff/" + (currentOrgCode || ''),
                updateUrl: prefix + "/editStaff/{id}",
                removeUrl: prefix + "/removeStaff",
                modalName: "机构人员",
                uniqueId: "staffId",
                columns: [{
                    checkbox: true
                }, {
                    field: 'index',
                    align: 'center',
                    title: "序号",
                    formatter: function (value, row, index) {
                        var columnIndex = $.common.sprintf("<input type='hidden' name='index' value='%s'>", $.table.serialNumber(index));
                        return columnIndex + $.table.serialNumber(index);
                    }
                }, {
                    field: 'avatar',
                    title: '头像',
                    width: 80,
                    formatter: function (value, row, index) {
                        if (value != null && value != '') {
                            return $.table.imageView(value, 300, 300);
                        } else {
                            return $.table.imageView('/img/default_people.png', 300, 300);
                        }
                    }
                }, {
                    field: 'peopleNameCn',
                    title: '中文名称'
                }, {
                    field: 'peopleNameEn',
                    title: '英文名称'
                }, {
                    field: 'position',
                    title: '职位'
                }, {
                    field: 'status',
                    title: '状态',
                    width: 120,
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(statusDatas, value);
                    }
                }, {
                    field: 'updateTime',
                    title: '修改时间'
                }, {
                    field: 'source',
                    title: '数据来源',
                    formatter: function (value, row, index) {
                        if (value != '' && value != null) {
                            let html = "";
                            var sourceArr = value.split(';');
                            if (sourceArr != null && sourceArr.length > 0) {
                                let urlReg = /[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+\.?/;
                                for (let i = 0; i < sourceArr.length; i++) {
                                    let source = sourceArr[i];
                                    let domainName = urlReg.exec(source);
                                    if (domainName != null && domainName != '') {
                                        if (i > 0) {
                                            html += '</br>'
                                        }
                                        html += "<a target='_blank' href='" + source + "' title='" + source + "'>" + (domainName[0] != null && domainName[0] != '' ? domainName[0] : source) + "</a>";
                                    }
                                }
                                if (html != null && html != '') {
                                    return html;
                                }
                            }
                        }
                        return $.table.tooltip(value, 8);
                    }
                }, {
                    title: '操作',
                    align: 'center',
                    formatter: function (value, row, index) {
                        let actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="editStaff(\'' + row.staffId + '\')"><i class="fa fa-edit"></i>修改</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="removeStaff(\'' + row.staffId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        }

        // 刷新人员列表表格
        function refreshStaffTable() {
            if (currentOrgCode) {
                console.log('刷新人员列表，机构编码:', currentOrgCode);
                // 更新隐藏字段
                $('#currentOrgCode').val(currentOrgCode);
                // 使用$.table.search方法刷新表格
                $.table.search('form-staff', 'bootstrap-table-staff');
            } else {
                console.log('无机构编码，无法刷新人员列表');
            }
        }

        // 添加人员
        function addStaff() {
            if (!currentOrgCode) {
                $.modal.alertWarning('请先选择机构');
                return;
            }
            $.modal.open("添加人员", prefix + "/addStaff/" + encodeURIComponent(currentOrgCode), 1200, 800);
        }

        // 编辑人员
        function editStaff(staffId) {
            if (staffId) {
                $.modal.open("修改人员", prefix + "/editStaff/" + staffId, 1200, 800);
            } else {
                var rows = $.table.selectColumns("staffId");
                if (rows.length == 0) {
                    $.modal.alertWarning("请选择要修改的数据");
                    return;
                }
                if (rows.length > 1) {
                    $.modal.alertWarning("请选择一条数据进行修改");
                    return;
                }
                $.modal.open("修改人员", prefix + "/editStaff/" + rows[0], 1200, 800);
            }
        }

        // 删除人员
        function removeStaff(staffId) {
            var ids = staffId || $.table.selectColumns("staffId");
            if (ids.length == 0) {
                $.modal.alertWarning("请选择要删除的数据");
                return;
            }
            $.modal.confirm("确认要删除选中的" + ids.length + "条数据吗？", function() {
                $.operate.submit(prefix + "/removeStaff", "post", "json", {ids: ids.join(",")});
            });
        }
    </script>
</body>
</html>
