<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('修改机构人员')" />
    <th:block th:include="include :: bootstrap-fileinput-css"/>
    <th:block th:include="include :: jasny-bootstrap-css" />
    <th:block th:include="include :: select2-css" />
    <th:block th:include="include :: bootstrap-tagsinput-css" />
    <style type="text/css">
        .bootstrap-tagsinput {
            width: 100%;
        }
        .label-info {
            background-color: #5bc0de;
        }
        .section-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            border-bottom: 2px solid #1ab394;
            padding-bottom: 5px;
            margin: 20px 0 15px 0;
        }
        .section-title:first-child {
            margin-top: 0;
        }
        .form-section {
            background: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-staff-edit" th:object="${staff}">
            <input name="staffId" type="hidden" th:field="*{staffId}">
            <input name="orgCode" type="hidden" th:field="*{orgCode}">
            <input name="peopleCode" type="hidden" th:field="*{peopleCode}">
            
            <!-- 基本信息部分 -->
            <div class="section-title">基本信息</div>
            <div class="form-section">
                <div class="row">
                    <div class="col-sm-9">
                        <div class="row">
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label is-required">国家/地区：</label>
                                    <div class="col-sm-8">
                                        <select name="country" id="country" class="form-control" th:with="type=${@dict.getType('sys_country')}" required>
                                            <option value="" style="color: #b6b6b6" disabled>选择国家/地区</option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{country}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label is-required">中文名称：</label>
                                    <div class="col-sm-8">
                                        <input name="peopleNameCn" th:field="*{peopleNameCn}" class="form-control" type="text" required>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">英文名称：</label>
                                    <div class="col-sm-8">
                                        <input name="peopleNameEn" th:field="*{peopleNameEn}" class="form-control" type="text">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">人员分类：</label>
                                    <div class="col-sm-8">
                                        <input name="peopleType" th:field="*{peopleType}" class="form-control" type="text">
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label is-required">状态：</label>
                                    <div class="col-sm-8">
                                        <select name="status" class="form-control" th:with="type=${@dict.getType('sys_staff_status')}" required>
                                            <option value="" style="color: #b6b6b6" disabled>选择状态</option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{status}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">人员编码：</label>
                                    <div class="col-sm-8">
                                        <input name="peopleCodeDisplay" th:value="*{peopleCode}" class="form-control" type="text" readonly="readonly">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-3">
                        <div class="form-group text-center">
                            <input type="hidden" name="avatar" id="avatar" th:field="*{avatar}">
                            <p class="user-info-head" onclick="peopleAvatar()">
                                <img class="img-lg" id="avatarUrl" th:src="*{avatar}" th:onerror="'this.src=\'' + @{'/img/default_people.png'} + '\''">
                            </p>
                            <p><input type="file" id="peopleAvatarInput" style="display: none;"><a onclick="peopleAvatar()">点击上传头像</a></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 详细信息部分 -->
            <div class="section-title">详细信息（可选）</div>
            <div class="form-section" th:object="${peopleMain}">
                <div class="row">
                    <div class="col-sm-4">
                        <div class="form-group">
                            <label class="col-sm-4 control-label">国家/地区：</label>
                            <div class="col-sm-8">
                                <select name="country" id="country" class="form-control" th:with="type=${@dict.getType('sys_country')}">
                                    <option value="" style="color: #b6b6b6" disabled>选择国家/地区</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{country}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <div class="form-group">
                            <label class="col-sm-4 control-label">性别：</label>
                            <div class="col-sm-8">
                                <div class="radio-box" th:each="dict : ${@dict.getType('sys_user_sex')}">
                                    <input type="radio" th:id="${'gender_' + dict.dictCode}" name="gender" th:value="${dict.dictValue}" th:field="*{gender}">
                                    <label th:for="${'gender_' + dict.dictCode}" th:text="${dict.dictLabel}"></label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <div class="form-group">
                            <label class="col-sm-4 control-label">人员所属类型：</label>
                            <div class="col-sm-8">
                                <select name="peopleTypeDetail" class="form-control" th:with="type=${@dict.getType('personnel_officer_type')}">
                                    <option value="" style="color: #b6b6b6" disabled>选择人员所属类型</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{peopleType}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-sm-4">
                        <div class="form-group">
                            <label class="col-sm-4 control-label">所属政党：</label>
                            <div class="col-sm-8">
                                <input name="party" th:field="*{party}" class="form-control" type="text">
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <div class="form-group">
                            <label class="col-sm-4 control-label">所属军兵种：</label>
                            <div class="col-sm-8">
                                <select name="troopsCategory" class="form-control" th:with="type=${@dict.getType('sys_troops_categories')}">
                                    <option value="" style="color: #b6b6b6" disabled>选择所属军兵种</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{troopsCategory}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <div class="form-group">
                            <label class="col-sm-4 control-label">军衔：</label>
                            <div class="col-sm-8">
                                <input name="militaryRank" th:field="*{militaryRank}" class="form-control" type="text">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-sm-4">
                        <div class="form-group">
                            <label class="col-sm-4 control-label">职业：</label>
                            <div class="col-sm-8">
                                <input name="occupation" th:field="*{occupation}" class="form-control" type="text">
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <div class="form-group">
                            <label class="col-sm-4 control-label">所在机构：</label>
                            <div class="col-sm-8">
                                <input name="orgName" th:field="*{orgName}" class="form-control" type="text">
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <div class="form-group">
                            <label class="col-sm-4 control-label">当前职务：</label>
                            <div class="col-sm-8">
                                <input name="post" th:field="*{post}" class="form-control" type="text">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-sm-4">
                        <div class="form-group">
                            <label class="col-sm-4 control-label">任职日期：</label>
                            <div class="col-sm-8">
                                <div class="row">
                                    <div class="col-sm-4">
                                        <select name="appointmentYear" id="appointmentYear" class="form-control">
                                            <option value="">年份</option>
                                        </select>
                                    </div>
                                    <div class="col-sm-4">
                                        <select name="appointmentMonth" id="appointmentMonth" class="form-control">
                                            <option value="">月份</option>
                                        </select>
                                    </div>
                                    <div class="col-sm-4">
                                        <select name="appointmentDay" id="appointmentDay" class="form-control">
                                            <option value="">日</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <div class="form-group">
                            <label class="col-sm-4 control-label">工作状态：</label>
                            <div class="col-sm-8">
                                <select name="workStatus" class="form-control">
                                    <option value="">选择工作状态</option>
                                    <option value="current">在职</option>
                                    <option value="former">离职</option>
                                    <option value="retired">退休</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <!-- 预留空间 -->
                    </div>
                </div>

                <div class="row">
                    <div class="col-sm-3">
                        <div class="form-group">
                            <label class="col-sm-4 control-label">出生日期：</label>
                            <div class="col-sm-8">
                                <div class="row">
                                    <div class="col-sm-4">
                                        <select name="birthdayYear" id="birthdayYear" class="form-control">
                                            <option value="">年份</option>
                                        </select>
                                    </div>
                                    <div class="col-sm-4">
                                        <select name="birthdayMonth" id="birthdayMonth" class="form-control">
                                            <option value="">月份</option>
                                        </select>
                                    </div>
                                    <div class="col-sm-4">
                                        <select name="birthdayDay" id="birthdayDay" class="form-control">
                                            <option value="">日</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-3">
                        <div class="form-group">
                            <label class="col-sm-4 control-label">年龄：</label>
                            <div class="col-sm-8">
                                <input name="age" id="age" th:field="*{age}" class="form-control" type="text" number="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-3">
                        <div class="form-group">
                            <label class="col-sm-4 control-label">出生地：</label>
                            <div class="col-sm-8">
                                <input name="birthplace" th:field="*{birthplace}" class="form-control" type="text">
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-3">
                        <div class="form-group">
                            <label class="col-sm-4 control-label">工作地点：</label>
                            <div class="col-sm-8">
                                <input name="workplace" th:field="*{workplace}" class="form-control" type="text">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-sm-3">
                        <div class="form-group">
                            <label class="col-sm-4 control-label">邮箱：</label>
                            <div class="col-sm-8">
                                <input name="email" th:field="*{email}" class="form-control" type="text">
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-3">
                        <div class="form-group">
                            <label class="col-sm-4 control-label">联系方式：</label>
                            <div class="col-sm-8">
                                <input name="telephone" th:field="*{telephone}" class="form-control" type="text">
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-3">
                        <div class="form-group">
                            <label class="col-sm-4 control-label">毕业院校：</label>
                            <div class="col-sm-8">
                                <input name="graduatedUniversity" th:field="*{graduatedUniversity}" class="form-control" type="text">
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-3">
                        <div class="form-group">
                            <label class="col-sm-4 control-label">学历：</label>
                            <div class="col-sm-8">
                                <input name="education" th:field="*{education}" class="form-control" type="text">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 简介信息部分 -->
            <div class="section-title">简介信息</div>
            <div class="form-section">
                <div class="alert alert-info">
                    <i class="fa fa-info-circle"></i>
                    <strong>操作提示：</strong>可以修改英文简介后点击"AI解析"按钮重新解析人员信息，未能填充的属性请手工维护。
                </div>

                <div class="form-group">
                    <label class="col-sm-1 control-label">英文简介：</label>
                    <div class="col-sm-10">
                        <textarea name="profileEn" id="profileEn" th:field="*{profileEn}" class="form-control" rows="4" placeholder="请输入英文简介，AI将根据此内容自动解析人员信息"></textarea>
                    </div>
                    <div class="col-sm-1">
                        <button type="button" class="btn btn-info btn-sm" onclick="parseEnglishBio()" id="parseBtn" style="margin-top: 10px;">
                            <i class="fa fa-magic"></i> AI解析
                        </button>
                        <div id="parseLoading" style="display: none; margin-top: 10px;">
                            <i class="fa fa-spinner fa-spin"></i> <span id="parseStatus">AI解析中...</span>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-sm-1 control-label">中文简介：</label>
                    <div class="col-sm-11">
                        <textarea name="profileCn" th:field="*{profileCn}" class="form-control" rows="4" placeholder="AI解析后会自动填充中文简介，也可手动输入"></textarea>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-sm-1 control-label is-required">数据来源：</label>
                    <div class="col-sm-11">
                        <input name="source" th:field="*{source}" class="form-control" type="text" placeholder="请输入数据来源URL或描述" required>
                    </div>
                </div>
            </div>

            <!-- 经历信息部分 -->
            <div class="section-title">经历信息</div>
            <div class="form-section">
                <div class="form-group">
                    <label class="col-sm-1 control-label">教育经历：</label>
                    <div class="col-sm-11">
                        <textarea name="educationalExperience" th:field="*{educationalExperience}" class="form-control" rows="4" placeholder="请输入教育经历，每条记录一行"></textarea>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-1 control-label">工作经历：</label>
                    <div class="col-sm-11">
                        <textarea name="assignments" th:field="*{assignments}" class="form-control" rows="4" placeholder="请输入工作经历，每条记录一行"></textarea>
                    </div>
                </div>
            </div>


        </form>
    </div>
    
    <div class="row">
        <div class="col-sm-offset-5 col-sm-10">
            <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>确 定</button>&nbsp;
            <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭</button>
        </div>
    </div>

    <th:block th:include="include :: footer" />
    <th:block th:include="include :: select2-js" />
    <th:block th:include="include :: jasny-bootstrap-js" />
    <th:block th:include="include :: bootstrap-fileinput-js"/>
    <th:block th:include="include :: bootstrap-tagsinput-js" />

    <script th:inline="javascript">
        var prefix = ctx + "system/architecture/personnel";

        $("#form-staff-edit").validate({
            focusCleanup: true
        });

        $(function() {
            // 初始化日期选择器
            initDateSelectors();

            // 初始化任职日期选择器
            initAppointmentDateSelectors();

            // 设置默认值
            setDefaultValues();
        });

        // 初始化日期选择器
        function initDateSelectors() {
            var birthdayYear = document.getElementById("birthdayYear");
            var date = new Date();
            var year = date.getFullYear();

            // 组建年份选择器
            for (var i = year; i >= year - 110; i--) {
                birthdayYear.options.add(new Option(i, i));
            }

            // 组建月份选择器
            var birthdayMonth = document.getElementById("birthdayMonth");
            for (var j = 1; j <= 12; j++) {
                if (j < 10) {
                    birthdayMonth.options.add(new Option('0' + j, '0' + j));
                } else {
                    birthdayMonth.options.add(new Option(j, j));
                }
            }

            // 组建日选择器
            let birthdayDay = document.getElementById("birthdayDay");
            for (let j = 1; j <= 31; j++) {
                if (j < 10) {
                    birthdayDay.options.add(new Option('0' + j, '0' + j));
                } else {
                    birthdayDay.options.add(new Option(j, j));
                }
            }

            // 年份变化事件
            $("#birthdayYear").change(function() {
                let birthday = $('#birthdayYear').select2('val');
                $('#age').val(year - birthday); // 计算年龄
                var birthdayYear = $('#birthdayYear option:selected').val();
                if (birthdayYear != '' && birthdayYear != null) {
                    $('#birthdayMonth').attr("disabled", false);
                } else {
                    $('#birthdayMonth').attr("disabled", true);
                    $("#birthdayMonth").select2("val", [""]);
                }
            });

            // 月份变化事件
            $("#birthdayMonth").change(function() {
                let birthdayMonth = $('#birthdayMonth option:selected').val();
                if (birthdayMonth != '' && birthdayMonth != null) {
                    $('#birthdayDay').attr("disabled", false);
                } else {
                    $("#birthdayDay").select2("val", [""]);
                    $('#birthdayDay').attr("disabled", true);
                }
            });
        }

        // 初始化任职日期选择器
        function initAppointmentDateSelectors() {
            var appointmentYear = document.getElementById("appointmentYear");
            var date = new Date();
            var year = date.getFullYear();

            // 组建年份选择器（从当前年份到过去50年）
            for (var i = year; i >= year - 50; i--) {
                appointmentYear.options.add(new Option(i, i));
            }

            // 组建月份选择器
            var appointmentMonth = document.getElementById("appointmentMonth");
            for (var j = 1; j <= 12; j++) {
                if (j < 10) {
                    appointmentMonth.options.add(new Option('0' + j, '0' + j));
                } else {
                    appointmentMonth.options.add(new Option(j, j));
                }
            }

            // 组建日选择器
            let appointmentDay = document.getElementById("appointmentDay");
            for (let j = 1; j <= 31; j++) {
                if (j < 10) {
                    appointmentDay.options.add(new Option('0' + j, '0' + j));
                } else {
                    appointmentDay.options.add(new Option(j, j));
                }
            }

            // 年份变化事件
            $("#appointmentYear").change(function() {
                var appointmentYear = $('#appointmentYear option:selected').val();
                if (appointmentYear != '' && appointmentYear != null) {
                    $('#appointmentMonth').attr("disabled", false);
                } else {
                    $('#appointmentMonth').attr("disabled", true);
                    $("#appointmentMonth").select2("val", [""]);
                }
            });

            // 月份变化事件
            $("#appointmentMonth").change(function() {
                let appointmentMonth = $('#appointmentMonth option:selected').val();
                if (appointmentMonth != '' && appointmentMonth != null) {
                    $('#appointmentDay').attr("disabled", false);
                } else {
                    $("#appointmentDay").select2("val", [""]);
                    $('#appointmentDay').attr("disabled", true);
                }
            });
        }

        // 设置默认值
        function setDefaultValues() {
            // 设置状态默认值
            let status = [[${staff.status}]];
            if (status) {
                $('select[name="status"]').val(status);
            }

            // 设置国家默认值
            let country = [[${peopleMain?.country}]];
            if (country) {
                $('#country').val(country);
            }

            // 设置军兵种默认值
            let troopsCategory = [[${peopleMain?.troopsCategory}]];
            if (troopsCategory) {
                $('select[name="troopsCategory"]').val(troopsCategory);
            }

            // 设置人员类型默认值
            let peopleTypeDetail = [[${peopleMain?.peopleType}]];
            if (peopleTypeDetail) {
                $('select[name="peopleTypeDetail"]').val(peopleTypeDetail);
            }

            // 设置出生日期
            let birthdayYear = [[${peopleMain?.birthdayYear}]];
            let birthdayMonth = [[${peopleMain?.birthdayMonth}]];
            let birthdayDay = [[${peopleMain?.birthdayDay}]];

            if (birthdayYear) {
                $('#birthdayYear').val(birthdayYear);
                $('#birthdayYear').trigger('change');

                if (birthdayMonth) {
                    setTimeout(function() {
                        $('#birthdayMonth').val(birthdayMonth);
                        $('#birthdayMonth').trigger('change');

                        if (birthdayDay) {
                            setTimeout(function() {
                                $('#birthdayDay').val(birthdayDay);
                            }, 300);
                        }
                    }, 300);
                }
            }

            // 设置任职日期
            let appointmentYear = [[${peopleMain?.appointmentYear}]];
            let appointmentMonth = [[${peopleMain?.appointmentMonth}]];
            let appointmentDay = [[${peopleMain?.appointmentDay}]];

            if (appointmentYear) {
                $('#appointmentYear').val(appointmentYear);
                $('#appointmentYear').trigger('change');

                if (appointmentMonth) {
                    setTimeout(function() {
                        $('#appointmentMonth').val(appointmentMonth);
                        $('#appointmentMonth').trigger('change');

                        if (appointmentDay) {
                            setTimeout(function() {
                                $('#appointmentDay').val(appointmentDay);
                            }, 300);
                        }
                    }, 300);
                }
            }

            // 设置工作状态
            let workStatus = [[${peopleMain?.workStatus}]];
            if (workStatus) {
                $('select[name="workStatus"]').val(workStatus);
            }
        }

        // 提交表单
        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTab(prefix + "/editStaff", $('#form-staff-edit').serialize());
            }
        }

        // 上传人员头像
        function peopleAvatar() {
            $('#peopleAvatarInput').trigger('click');
        }

        $("#peopleAvatarInput").change(function() {
            var data = new FormData();
            data.append("file", $("#peopleAvatarInput")[0].files[0]);
            $.ajax({
                type: "POST",
                url: ctx + "common/upload/img",
                data: data,
                cache: false,
                contentType: false,
                processData: false,
                dataType: 'json',
                success: function(result) {
                    console.log('头像上传结果:', result);
                    if (result.code == 0 || result.code == 200 || result.code == '0' || result.code == '200') {
                        $("#avatarUrl").attr("src", result.url);
                        $("#avatar").val(result.url);
                        $.modal.msgSuccess("头像上传成功");
                    } else {
                        $.modal.alertError("头像上传失败：" + (result.msg || "未知错误"));
                    }
                },
                error: function(xhr, status, error) {
                    console.error('头像上传失败:', error);
                    $.modal.alertError("头像上传失败，请检查网络连接");
                }
            });
        });

        // AI解析英文简介功能
        function parseEnglishBio() {
            var profileEn = $('#profileEn').val().trim();
            if (!profileEn) {
                $.modal.alertWarning('请先输入英文简介内容');
                return;
            }

            // 显示加载状态
            $('#parseBtn').hide();
            $('#parseLoading').show();
            $('#parseStatus').text('AI正在解析人员信息...');

            $.ajax({
                type: "POST",
                url: prefix + "/parseEnglishBio",
                data: {
                    'profileEn': profileEn
                },
                dataType: 'json',
                timeout: 120000, // 2分钟超时
                success: function(result) {
                    $('#parseBtn').show();
                    $('#parseLoading').hide();

                    if (result.code == 0) {
                        // 解析成功，填充表单数据
                        $('#parseStatus').text('正在填充表单数据...');
                        setTimeout(function() {
                            fillFormWithParsedData(result.data);
                            var message = 'AI解析完成，已自动填充相关字段';
                            if (result.data.profileCn) {
                                message += '（包含中文翻译）';
                            }
                            $.modal.msgSuccess(message);
                        }, 500);
                    } else {
                        $.modal.alertError('AI解析失败：' + (result.msg || '未知错误'));
                    }
                },
                error: function(xhr, status, error) {
                    $('#parseBtn').show();
                    $('#parseLoading').hide();
                    if (status === 'timeout') {
                        $.modal.alertError('AI解析超时，请重试或检查网络连接');
                    } else {
                        $.modal.alertError('AI解析服务连接失败，请检查网络连接');
                    }
                }
            });
        }

        // 填充解析后的数据到表单
        function fillFormWithParsedData(data) {
            if (!data) return;

            // 填充基本信息
            if (data.nameEn) {
                $('input[name="peopleNameEn"]').val(data.nameEn);
            }
            if (data.nameCn) {
                $('input[name="peopleNameCn"]').val(data.nameCn);
            }
            if (data.militaryRank) {
                $('input[name="militaryRank"]').val(data.militaryRank);
            }
            if (data.post) {
                $('input[name="post"]').val(data.post);
                $('input[name="position"]').val(data.post); // 同时填充职位
            }
            if (data.orgName) {
                $('input[name="orgName"]').val(data.orgName);
            }
            if (data.birthplace) {
                $('input[name="birthplace"]').val(data.birthplace);
            }
            if (data.graduatedUniversity) {
                $('input[name="graduatedUniversity"]').val(data.graduatedUniversity);
            }
            if (data.education) {
                $('input[name="education"]').val(data.education);
            }

            // 填充翻译后的中文简介
            if (data.profileCn) {
                $('textarea[name="profileCn"]').val(data.profileCn);
            }

            // 处理出生日期
            if (data.birthdayYear) {
                setSelect2Value('#birthdayYear', data.birthdayYear);
                $('#birthdayYear').trigger('change');
                if (data.birthdayMonth) {
                    setTimeout(function() {
                        setSelect2Value('#birthdayMonth', data.birthdayMonth);
                        $('#birthdayMonth').trigger('change');
                        if (data.birthdayDay) {
                            setTimeout(function() {
                                setSelect2Value('#birthdayDay', data.birthdayDay);
                            }, 300);
                        }
                    }, 300);
                }
            }

            // 处理性别
            if (data.gender) {
                setTimeout(function() {
                    $('input[name="gender"]').prop('checked', false);
                    var genderRadio = $('input[name="gender"][value="' + data.gender + '"]');
                    if (genderRadio.length > 0) {
                        genderRadio.prop('checked', true);
                        genderRadio.trigger('change');
                    }
                }, 200);
            }

            // 处理国家
            if (data.country) {
                setTimeout(function() {
                    setSelect2Value('#country', data.country);
                }, 100);
            }

            // 处理军兵种
            if (data.troopsCategory) {
                setTimeout(function() {
                    setSelect2Value('select[name="troopsCategory"]', data.troopsCategory);
                }, 100);
            }

            // 处理人员所属类型
            if (data.peopleType) {
                setTimeout(function() {
                    setSelect2Value('select[name="peopleTypeDetail"]', data.peopleType);
                }, 100);
            }
        }

        // Select2组件设置值的辅助函数
        function setSelect2Value(selector, value) {
            var $element = $(selector);
            if ($element.length > 0) {
                try {
                    if ($element.hasClass('select2-hidden-accessible')) {
                        $element.select2("val", [value]);
                    } else {
                        $element.val(value);
                    }
                    $element.trigger('change');
                } catch (e) {
                    $element.val(value).trigger('change');
                }
            }
        }
    </script>
</body>
</html>
