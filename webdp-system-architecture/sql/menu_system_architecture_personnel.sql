-- 体系结构人员管理菜单配置
-- 插入体系结构人员管理菜单

-- 1. 插入主菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('体系结构人员管理', 0, 6, 'systemArchitecturePersonnel', NULL, 1, 0, 'M', '0', '0', NULL, 'fa fa-sitemap', 'admin', sysdate(), '', NULL, '体系结构人员管理菜单');

-- 获取刚插入的菜单ID（需要根据实际情况调整）
SET @parent_menu_id = LAST_INSERT_ID();

-- 2. 插入子菜单 - 体系结构人员管理
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('体系结构人员管理', @parent_menu_id, 1, 'personnel', 'system/architecture/personnel/systemArchitecturePersonnel', 1, 0, 'C', '0', '0', 'system:architecture:personnel:view', 'fa fa-users', 'admin', sysdate(), '', NULL, '体系结构人员管理菜单');

-- 获取体系结构人员管理菜单ID
SET @personnel_menu_id = LAST_INSERT_ID();

-- 3. 插入按钮权限
-- 查询权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('体系结构人员查询', @personnel_menu_id, 1, '', '', 1, 0, 'F', '0', '0', 'system:architecture:personnel:list', '', 'admin', sysdate(), '', NULL, '');

-- 新增权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('体系结构人员新增', @personnel_menu_id, 2, '', '', 1, 0, 'F', '0', '0', 'system:architecture:personnel:add', '', 'admin', sysdate(), '', NULL, '');

-- 修改权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('体系结构人员修改', @personnel_menu_id, 3, '', '', 1, 0, 'F', '0', '0', 'system:architecture:personnel:edit', '', 'admin', sysdate(), '', NULL, '');

-- 删除权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('体系结构人员删除', @personnel_menu_id, 4, '', '', 1, 0, 'F', '0', '0', 'system:architecture:personnel:remove', '', 'admin', sysdate(), '', NULL, '');

-- 导出权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('体系结构人员导出', @personnel_menu_id, 5, '', '', 1, 0, 'F', '0', '0', 'system:architecture:personnel:export', '', 'admin', sysdate(), '', NULL, '');

-- 4. 为admin角色分配权限（假设admin角色ID为1）
-- 获取所有新插入的菜单ID
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT 1, menu_id FROM sys_menu WHERE menu_name IN (
    '体系结构人员管理',
    '体系结构人员查询',
    '体系结构人员新增', 
    '体系结构人员修改',
    '体系结构人员删除',
    '体系结构人员导出'
) AND create_by = 'admin' AND create_time >= DATE_SUB(NOW(), INTERVAL 1 MINUTE);

-- 查询插入结果
SELECT 
    m.menu_id,
    m.menu_name,
    m.parent_id,
    m.order_num,
    m.path,
    m.component,
    m.perms,
    m.menu_type
FROM sys_menu m 
WHERE m.menu_name LIKE '%体系结构人员%' 
ORDER BY m.parent_id, m.order_num;
