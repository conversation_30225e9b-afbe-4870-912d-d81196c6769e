# 体系结构人员管理功能说明

## 功能概述

体系结构人员管理是一个综合性的人员管理界面，融合了现有系统中的体系、机构、人员管理功能。该界面提供了一个统一的视图来管理体系结构下的机构及其人员信息。

## 界面布局

### 1. 左侧：体系结构树
- 显示完整的体系结构层次
- 支持体系选择和结构节点点击
- 树形结构清晰展示组织架构

### 2. 右侧上部：机构信息展示
- 点击体系结构树中的机构节点时显示
- 展示机构的详细信息，包括：
  - 机构名称（中英文）
  - 机构类型
  - 机构编码
  - 国家/地区
  - 地址、联系方式
  - 人员数量等

### 3. 右侧下部：人员列表管理
- 显示选中机构的人员列表
- 支持人员的增删改查操作
- 融合了机构人员和详细人员信息的管理

## 主要功能

### 1. 体系结构浏览
- 选择不同体系查看其结构树
- 点击结构节点查看对应机构信息
- 支持分类节点和机构节点的区分显示

### 2. 机构信息查看
- 自动加载选中机构的详细信息
- 信息展示包括基本信息和联系信息
- 支持机构类型、地址等多维度信息

### 3. 人员管理
#### 3.1 人员列表
- 显示机构下的所有人员
- 支持按姓名、职位、状态等条件搜索
- 列表显示头像、姓名、职位、状态等关键信息

#### 3.2 添加人员
- **基本信息**：中英文姓名、职位、人员分类、状态等
- **详细信息**：国家、性别、政党、军兵种、军衔等
- **个人信息**：出生日期、年龄、出生地、工作地点等
- **联系信息**：邮箱、电话等
- **教育信息**：毕业院校、学历等
- **简介信息**：中英文简介，支持AI解析

#### 3.3 编辑人员
- 支持修改所有人员信息
- 保持机构人员和详细人员信息的同步
- 支持头像上传和更新

#### 3.4 AI解析功能
- 输入英文简介后，可使用AI解析功能
- 自动提取人员的基本信息（姓名、职位、军衔等）
- 自动翻译英文简介为中文
- 智能填充表单字段

## 技术特点

### 1. 数据融合
- 将机构人员表（idw_org_staff）和人员详细信息表（idw_people_main）进行融合
- 通过人员编码（peopleCode）建立关联关系
- 支持只维护基本机构人员信息，也支持维护详细人员档案

### 2. 界面集成
- 左右分栏布局，操作流畅
- 树形结构与详情展示的有机结合
- 响应式设计，适配不同屏幕尺寸

### 3. 权限控制
- 基于Shiro的细粒度权限控制
- 支持查看、新增、修改、删除等不同权限级别
- 按钮级别的权限控制

## 文件结构

```
webdp-system-architecture/
├── src/main/java/com/lirong/system/architecture/controller/
│   └── IdwOrgSystemArchitecturePersonnelController.java  # 主控制器
├── src/main/resources/templates/system/architecture/personnel/
│   ├── systemArchitecturePersonnel.html                 # 主页面
│   ├── addStaff.html                                    # 添加人员页面
│   └── editStaff.html                                   # 编辑人员页面
├── sql/
│   └── menu_system_architecture_personnel.sql           # 菜单配置SQL
└── README_体系结构人员管理.md                            # 本说明文档
```

## 部署说明

### 1. 数据库配置
执行 `sql/menu_system_architecture_personnel.sql` 脚本添加菜单配置。

### 2. 权限配置
确保用户具有以下权限：
- `system:architecture:personnel:view` - 查看权限
- `system:architecture:personnel:add` - 新增权限
- `system:architecture:personnel:edit` - 修改权限
- `system:architecture:personnel:remove` - 删除权限

### 3. 依赖模块
确保以下模块正常运行：
- webdp-system-architecture（体系结构模块）
- webdp-organization-staff（机构人员模块）
- webdp-personnel-common（人员通用模块）

## 使用流程

1. **选择体系**：在左侧下拉框中选择要管理的体系
2. **浏览结构**：在体系结构树中点击不同节点
3. **查看机构**：点击机构节点查看机构详细信息
4. **管理人员**：在人员列表中进行增删改查操作
5. **AI辅助**：在添加/编辑人员时，可使用AI解析功能快速填充信息

## 注意事项

1. **数据一致性**：机构人员信息和详细人员信息通过peopleCode关联，请确保数据一致性
2. **权限检查**：所有操作都会进行权限检查，请确保用户具有相应权限
3. **AI功能**：AI解析功能需要配置本地AI服务，请确保服务可用
4. **文件上传**：头像上传功能需要配置文件上传路径

## 扩展说明

该功能设计为可扩展的架构，可以根据需要：
- 添加更多的人员信息字段
- 扩展AI解析的功能范围
- 增加更多的搜索和筛选条件
- 支持批量操作功能
