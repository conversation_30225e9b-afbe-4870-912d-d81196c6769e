package com.lirong.personnel.service;

import java.util.List;

import com.lirong.common.service.CascadeDeleteService;
import com.lirong.common.service.StatisticalService;
import com.lirong.personnel.domain.IdwPeoplePartyWork;

/**
 * 人大、政协任职经历Service接口
 *
 * <AUTHOR>
 * @date 2021-06-11
 */
public interface IdwPeoplePartyWorkService extends CascadeDeleteService, StatisticalService {
    /**
     * 查询人大、政协任职经历
     *
     * @param experienceId 人大、政协任职经历ID
     * @return 人大、政协任职经历
     */
    public IdwPeoplePartyWork selectIdwPeoplePartyWorkById(Long experienceId);

    /**
     * 查询人大、政协任职经历列表
     *
     * @param idwPeoplePartyWork 人大、政协任职经历
     * @return 人大、政协任职经历集合
     */
    public List<IdwPeoplePartyWork> selectIdwPeoplePartyWorkList(IdwPeoplePartyWork idwPeoplePartyWork);

    /**
     * 新增人大、政协任职经历
     *
     * @param idwPeoplePartyWork 人大、政协任职经历
     * @return 结果
     */
    public int insertIdwPeoplePartyWork(IdwPeoplePartyWork idwPeoplePartyWork);

    /**
     * 修改人大、政协任职经历
     *
     * @param idwPeoplePartyWork 人大、政协任职经历
     * @return 结果
     */
    public int updateIdwPeoplePartyWork(IdwPeoplePartyWork idwPeoplePartyWork);

    /**
     * 批量删除人大、政协任职经历
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteIdwPeoplePartyWorkByIds(String ids);

    /**
     * 校验Excel导入数据
     *
     * @param partyWorkList 人大、政协任职经历集合
     * @param type          类型 人大 政协
     * @return 结果
     */
    public List<String> verifyImportPartyWork(List<IdwPeoplePartyWork> partyWorkList, String type);

    /**
     * 导入人大、政协任职经历信息
     *
     * @param updateSupport 是否支持更新, 如果已存在, 则进行更新
     * @param operName      操作用户
     * @param type          类型 人大 政协
     * @return 结果
     */
    public String importPartyWork(boolean updateSupport, String operName, String type);
}
