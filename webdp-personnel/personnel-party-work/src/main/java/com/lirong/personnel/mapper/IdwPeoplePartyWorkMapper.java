package com.lirong.personnel.mapper;

import java.util.List;

import com.lirong.personnel.domain.IdwPeoplePartyWork;
import org.apache.ibatis.annotations.Param;

/**
 * 人大、政协任职经历Mapper接口
 *
 * <AUTHOR>
 * @date 2021-06-11
 */
public interface IdwPeoplePartyWorkMapper {
    /**
     * 查询人大、政协任职经历
     *
     * @param experienceId 人大、政协任职经历ID
     * @return 人大、政协任职经历
     */
    public IdwPeoplePartyWork selectIdwPeoplePartyWorkById(Long experienceId);

    /**
     * 查询人大、政协任职经历列表
     *
     * @param idwPeoplePartyWork 人大、政协任职经历
     * @return 人大、政协任职经历集合
     */
    public List<IdwPeoplePartyWork> selectIdwPeoplePartyWorkList(IdwPeoplePartyWork idwPeoplePartyWork);

    /**
     * 新增人大、政协任职经历
     *
     * @param idwPeoplePartyWork 人大、政协任职经历
     * @return 结果
     */
    public int insertIdwPeoplePartyWork(IdwPeoplePartyWork idwPeoplePartyWork);

    /**
     * 修改人大、政协任职经历
     *
     * @param idwPeoplePartyWork 人大、政协任职经历
     * @return 结果
     */
    public int updateIdwPeoplePartyWork(IdwPeoplePartyWork idwPeoplePartyWork);

    /**
     * 批量删除人大、政协任职经历
     *
     * @param experienceIds 需要删除的数据ID
     * @param loginName     当前登录用户
     * @return 结果
     */
    public int deleteIdwPeoplePartyWorkByIds(@Param("experienceIds") String[] experienceIds, @Param("loginName") String loginName);

    /**
     * 查询数据是否存在
     *
     * @param peopleCode    人员编码
     * @param type          类型
     * @param startDate     开始日期
     * @param endDate       结束日期
     * @param sessionNum    届数
     * @param levelNation   级别（国家）
     * @param levelProvince 级别（省/自治区/直辖市）
     * @param levelCity     级别（市/自治州）
     * @param levelCounty   级别（区/县）
     * @param post          政协职务|人大职务
     * @return 结果
     */
    public IdwPeoplePartyWork verifyIsExist(@Param("peopleCode") String peopleCode, @Param("type") String type, @Param("startDate") String startDate, @Param("endDate") String endDate, @Param("sessionNum") String sessionNum, @Param("levelNation") String levelNation, @Param("levelProvince") String levelProvince, @Param("levelCity") String levelCity, @Param("levelCounty") String levelCounty, @Param("post") String post);


    /**
     * 根据人员编码删除人员人大、政协任职经历
     *
     * @param peopleCodes 人员编码
     * @param loginName   当前登录用户
     * @param deleteTime  删除时间
     * @return 结果
     */
    public int deleteByPeopleCodes(@Param("peopleCodes") String[] peopleCodes, @Param("loginName") String loginName, @Param("deleteTime") String deleteTime);

    /**
     * 根据人员编码查询数据量
     *
     * @param peopleCode 人员编码
     * @return 结果
     */
    public int selectCountByPeopleCode(String peopleCode);
}
