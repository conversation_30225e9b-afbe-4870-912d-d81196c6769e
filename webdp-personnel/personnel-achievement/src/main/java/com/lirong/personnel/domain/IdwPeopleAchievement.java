package com.lirong.personnel.domain;

import com.lirong.common.annotation.Excel;
import com.lirong.common.core.domain.BaseEntity;
import com.lirong.common.utils.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 人员工作成果对象 idw_people_achievement
 *
 * <AUTHOR>
 * @date 2021-06-09
 */
public class IdwPeopleAchievement extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 成果主键
     */
    private Long achievementId;

    /**
     * 人员编码
     */
    @Excel(name = "人员编码|人物编码")
    private String peopleCode;

    /**
     * 成果类型；研究报告、主持项目、制定标准
     */
    private String achievementType;

    /**
     * 中文名称
     */
    @Excel(name = "报告名称|项目名称|标准名称|中文名称")
    private String name;

    /**
     * 英文名称
     */
    @Excel(name = "英文名称")
    private String nameEn;

    /**
     * 参与者（作者/项目人员）
     */
    @Excel(name = "作者|项目人员")
    private String participant;

    /**
     * 职责
     */
    @Excel(name = "职责|项目角色")
    private String duty;

    /**
     * 成果时间（发表时间/项目时间）
     */
    @Excel(name = "项目时间|发表时间|时间")
    private String achievementDate;

    /**
     * 开始日期
     */
    @Excel(name = "开始日期")
    private String startDate;

    /**
     * 结束日期
     */
    @Excel(name = "结束日期")
    private String endDate;

    /**
     * 项目来源
     */
    @Excel(name = "项目来源")
    private String projectResource;

    /**
     * 提交部门
     */
    @Excel(name = "提交部门")
    private String submittingDepartment;

    /**
     * 级别；国家级/省级/市级
     */
    @Excel(name = "级别")
    private String level;

    /**
     * 中文内容
     */
    @Excel(name = "中文内容")
    private String contentCn;

    /**
     * 英文内容
     */
    @Excel(name = "英文内容")
    private String contentEn;

    /**
     * 存储路径
     */
    @Excel(name = "附件文档")
    private String storagePath;

    /**
     * 文件名称
     */
    @Excel(name = "文件名称|文件名")
    private String fileName;

    /**
     * 数据来源
     */
    @Excel(name = "数据来源")
    private String source;

    /**
     * 是否删除，0-未删除，1-已删除
     */
    private Integer isDelete;

    private String startYear;

    private String startMonth;

    private String startDay;

    private String endYear;

    private String endMonth;

    private String endDay;

    public void setAchievementId(Long achievementId) {
        this.achievementId = achievementId;
    }

    public Long getAchievementId() {
        return achievementId;
    }

    public void setPeopleCode(String peopleCode) {
        this.peopleCode = peopleCode;
    }

    public String getPeopleCode() {
        return peopleCode;
    }

    public void setAchievementType(String achievementType) {
        this.achievementType = achievementType;
    }

    public String getAchievementType() {
        return achievementType;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public void setParticipant(String participant) {
        this.participant = participant;
    }

    public String getParticipant() {
        return participant;
    }

    public void setDuty(String duty) {
        this.duty = duty;
    }

    public String getDuty() {
        return duty;
    }

    public void setAchievementDate(String achievementDate) {
        this.achievementDate = achievementDate;
    }

    public String getAchievementDate() {
        return achievementDate;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public void setProjectResource(String projectResource) {
        this.projectResource = projectResource;
    }

    public String getProjectResource() {
        return projectResource;
    }

    public void setSubmittingDepartment(String submittingDepartment) {
        this.submittingDepartment = submittingDepartment;
    }

    public String getSubmittingDepartment() {
        return submittingDepartment;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public String getLevel() {
        return level;
    }

    public String getContentCn() {
        return contentCn;
    }

    public void setContentCn(String contentCn) {
        this.contentCn = contentCn;
    }

    public String getContentEn() {
        return contentEn;
    }

    public void setContentEn(String contentEn) {
        this.contentEn = contentEn;
    }

    public String getStoragePath() {
        return storagePath;
    }

    public void setStoragePath(String storagePath) {
        this.storagePath = storagePath;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getSource() {
        return StringUtils.isNotBlank(source) ? source.replaceAll("；", ";").replaceAll("(\r\n|\r|\n|\n\r)", ";") : source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public String getStartYear() {
        return startYear;
    }

    public void setStartYear(String startYear) {
        this.startYear = startYear;
    }

    public String getStartMonth() {
        return startMonth;
    }

    public void setStartMonth(String startMonth) {
        this.startMonth = startMonth;
    }

    public String getStartDay() {
        return startDay;
    }

    public void setStartDay(String startDay) {
        this.startDay = startDay;
    }

    public String getEndYear() {
        return endYear;
    }

    public void setEndYear(String endYear) {
        this.endYear = endYear;
    }

    public String getEndMonth() {
        return endMonth;
    }

    public void setEndMonth(String endMonth) {
        this.endMonth = endMonth;
    }

    public String getEndDay() {
        return endDay;
    }

    public void setEndDay(String endDay) {
        this.endDay = endDay;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("achievementId", getAchievementId())
                .append("peopleCode", getPeopleCode())
                .append("achievementType", getAchievementType())
                .append("name", getName())
                .append("nameEn", getNameEn())
                .append("participant", getParticipant())
                .append("duty", getDuty())
                .append("achievementDate", getAchievementDate())
                .append("startDate", getStartDate())
                .append("endDate", getEndDate())
                .append("projectResource", getProjectResource())
                .append("submittingDepartment", getSubmittingDepartment())
                .append("level", getLevel())
                .append("contentCn", getContentCn())
                .append("contentEn", getContentEn())
                .append("storagePath", getStoragePath())
                .append("fileName", getFileName())
                .append("source", getSource())
                .append("isDelete", getIsDelete())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("startYear", getStartYear())
                .append("startMonth", getStartMonth())
                .append("startDay", getStartDay())
                .append("endYear", getEndYear())
                .append("endMonth", getEndMonth())
                .append("endDay", getEndDay())
                .toString();
    }
}
