<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="form-enactmentStandard">
                <input type="hidden" name="peopleCode" th:value="${peopleCode}">
                <input type="hidden" name="achievementType" value="制定标准">
                <div class="select-list">
                    <ul>
                        <li>
                            <label>标准名称：</label>
                            <input type="text" name="name"/>
                        </li>
                        <li>
                            <label>职责：</label>
                            <input type="text" name="duty"/>
                        </li>
                        <li>
                            <label>级别：</label>
                            <select name="level" th:with="type=${@dict.getType('personnel_standard_level')}">
                                <option value="">所有</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                        th:value="${dict.dictValue}"></option>
                            </select>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search('form-enactmentStandard', 'bootstrap-table-enactmentStandard')"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset('form-enactmentStandard', 'bootstrap-table-enactmentStandard')"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar-enactmentStandard" role="group">
            <a class="btn btn-success" onclick="$.operate.add(null, null, 750)" shiro:hasPermission="people:achievement:add">
                <i class="fa fa-plus"></i> 添加
            </a>
            <a class="btn btn-primary single disabled" onclick="$.operate.edit(null, null, 750)" shiro:hasPermission="people:achievement:edit">
                <i class="fa fa-edit"></i> 修改
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="people:achievement:remove">
                <i class="fa fa-remove"></i> 删除
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table-enactmentStandard"></table>
        </div>
    </div>
</div>

<script th:inline="javascript">
    let editFlag = [[${@permission.hasPermi('people:achievement:edit')}]];
    let removeFlag = [[${@permission.hasPermi('people:achievement:remove')}]];
    let achievementTypeDatas = [[${@dict.getType('personnel_achievement_type')}]];
    let levelDatas = [[${@dict.getType('personnel_standard_level')}]];
    let prefix = ctx + "people/achievement";

    if (isView){
        //隐藏工具栏数据操作按钮
        document.getElementById("toolbar-enactmentStandard").style.display="none";
    }

    let type = [[${type}]] ? '/' + [[${type}]] : "";
    $(function () {
        let options = {
            id: "bootstrap-table-enactmentStandard",          // 指定表格ID
            toolbar: "toolbar-enactmentStandard",   // 指定工具栏ID
            formId: "form-enactmentStandard",
            url: prefix + "/list",
            createUrl: prefix + "/add" + type + "/" + peopleCode,
            updateUrl: prefix + "/edit" + type + "/{id}",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            modalName: "人员制定标准",
            uniqueId: "achievementId",
            columns: [{
                checkbox: true
            },
            {
                align: 'center',
                title: "序号",
                width: 60,
                formatter: function (value, row, index) {
                    return $.table.serialNumber(index);
                }
            },
            {
                field: 'name',
                title: '标准名称'
            },
            {
                field: 'achievementDate',
                title: '时间'
            },
            {
                field: 'duty',
                title: '职责'
            },
            {
                field: 'level',
                title: '级别',
                formatter: function (value, row, index) {
                    return $.table.selectDictLabel(levelDatas, value);
                }
            },
            {
                title: '操作',
                align: 'center',
                formatter: function (value, row, index) {
                    let actions = [];
                    if (isView) {
                        actions.push('<a class="btn btn-primary btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="edit(\'' + row.achievementId + '\')"><i class="fa fa-search"></i>查看</a> ');
                    } else {
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.achievementId + '\', null, 750)"><i class="fa fa-edit"></i>修改</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.achievementId + '\')"><i class="fa fa-remove"></i>删除</a> ');
                    }
                    return actions.join('');
                }
            }]
        };
        $.table.init(options);
    });

    function edit(achievementId) {
        $.modal.open('查看人员制定标准', prefix + "/edit/" + type + "/" +achievementId + '/' + isView, null, 750);
    }
</script>