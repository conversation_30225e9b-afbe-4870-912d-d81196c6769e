<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改工作经历')" />
    <th:block th:include="include :: select2-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-work-edit" th:object="${idwPeopleWorkExperience}">
            <input name="experienceId" th:field="*{experienceId}" type="hidden">
            <input name="peopleCode" th:field="*{peopleCode}" type="hidden">
            <input name="orgCode" th:field="*{orgCode}" type="hidden">
            <div class="form-group">    
                <label class="col-sm-3 control-label">单位名称：</label>
                <div class="col-sm-8">
                    <input name="companyName" th:field="*{companyName}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">工作地点：</label>
                <div class="col-sm-8">
                    <input name="location" th:field="*{location}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">职位名称：</label>
                <div class="col-sm-8">
                    <input name="title" th:field="*{title}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">开始日期：</label>
                <div class="col-sm-8">
                    <div class="row">
                        <div class="col-sm-4">
                            <select  name="startYear" id="startYear" class="form-control">
                                <option value="">年份</option>
                            </select>
                        </div>
                        <div class="col-sm-4">
                            <select  name="startMonth" id="startMonth" class="form-control">
                                <option value="">月份</option>
                            </select>
                        </div>
                        <div class="col-sm-4">
                            <select name="startDay" id="startDay" class="form-control" disabled="disabled">
                                <option value="">日</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">结束日期：</label>
                <div class="col-sm-8">
                    <div class="row">
                        <div class="col-sm-4">
                            <select  name="endYear" id="endYear" class="form-control">
                                <option value="">年份</option>
                            </select>
                        </div>
                        <div class="col-sm-4">
                            <select  name="endMonth" id="endMonth" class="form-control">
                                <option value="">月份</option>
                            </select>
                        </div>
                        <div class="col-sm-4">
                            <select name="endDay" id="endDay" class="form-control" disabled="disabled">
                                <option value="">日</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">工作描述：</label>
                <div class="col-sm-8">
                    <textarea name="workDescCn" class="form-control" rows="12">[[*{workDescCn}]]</textarea>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">数据来源：</label>
                <div class="col-sm-8">
                    <input name="source" th:field="*{source}" class="form-control" required>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: select2-js" />
    <script th:inline="javascript">

        var startYear = document.getElementById("startYear");
        var endYear = document.getElementById("endYear");
        var date = new Date();
        var year = date.getFullYear();
        //组建年份选择器
        for (var i = year; i >= year - 110; i--) {
            startYear.options.add(new Option(i, i));
            endYear.options.add(new Option(i, i));
        }

        //组建月份选择器
        var startMonth = document.getElementById("startMonth");
        var endMonth = document.getElementById("endMonth");
        for (var j = 1; j <= 12; j++) {
            if (j < 10){
                startMonth.options.add(new Option('0'+j, '0'+j));
                endMonth.options.add(new Option('0'+j, '0'+j));
            }else{
                startMonth.options.add(new Option(j, j));
                endMonth.options.add(new Option(j, j));
            }
        }
        //组建日选择器
        let startDay = document.getElementById("startDay");
        let endDay = document.getElementById("endDay");
        for (let j = 1; j <= 31; j++) {
            if (j < 10){
                startDay.options.add(new Option('0'+j, '0'+j));
                endDay.options.add(new Option('0'+j, '0'+j));
            }else{
                startDay.options.add(new Option(j, j));
                endDay.options.add(new Option(j, j));
            }
        }
        // idwPeopleWorkExperience
        let startYearData = [[${idwPeopleWorkExperience.startYear}]] ? [[${idwPeopleWorkExperience.startYear}]] : '';
        let startMonthData = [[${idwPeopleWorkExperience.startMonth}]] ? [[${idwPeopleWorkExperience.startMonth}]] : '';
        let startDayData = [[${idwPeopleWorkExperience.startDay}]] ? [[${idwPeopleWorkExperience.startDay}]] : '';
        let endYearData = [[${idwPeopleWorkExperience.endYear}]] ? [[${idwPeopleWorkExperience.endYear}]] : '';
        let endMonthData = [[${idwPeopleWorkExperience.endMonth}]] ? [[${idwPeopleWorkExperience.endMonth}]] : '';
        let endDayData = [[${idwPeopleWorkExperience.endDay}]] ? [[${idwPeopleWorkExperience.endDay}]] : '';

        $("#startYear").val(startYearData)
        $("#startMonth").val(startMonthData)
        $("#startDay").val(startDayData)
        if (startYearData != '' && startYearData != null){
            $('#startMonth').attr("disabled",false);
        }
        if (startMonthData != '' && startMonthData != null){
            $('#startDay').attr("disabled",false);
        }
        $("#endYear").val(endYearData)
        $("#endMonth").val(endMonthData)
        $("#endDay").val(endDayData)
        if (endYearData != '' && endYearData != null){
            $('#endMonth').attr("disabled",false);
        }
        if (endMonthData != '' && endMonthData != null){
            $('#endDay').attr("disabled",false);
        }


        $("#startYear").change(function(){
            var startYear = $('#startYear option:selected') .val();
            if (startYear != '' && startYear != null){
                $('#startMonth').attr("disabled",false);
            }else{
                $('#startMonth').attr("disabled",true);
                $("#startMonth").select2("val", [""]);
            }
        });

        $("#startMonth").change(function(){
            let startMonth = $('#startMonth option:selected') .val();
            if (startMonth != '' && startMonth != null){
                $('#startDay').attr("disabled",false);
            }else{
                $("#startDay").select2("val", [""]);
                $('#startDay').attr("disabled",true);
            }
        });

        $("#endYear").change(function(){
            var endYear = $('#endYear option:selected') .val();
            if (endYear != '' && endYear != null){
                $('#endMonth').attr("disabled",false);
            }else{
                $('#endMonth').attr("disabled",true);
                $("#endMonth").select2("val", [""]);
            }
        });

        $("#endMonth").change(function(){
            let endMonth = $('#endMonth option:selected') .val();
            if (endMonth != '' && endMonth != null){
                $('#endDay').attr("disabled",false);
            }else{
                $("#endDay").select2("val", [""]);
                $('#endDay').attr("disabled",true);
            }
        });

        var prefix = ctx + "people/work";
        $("#form-work-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/editWork", $('#form-work-edit').serialize());
            }
        }
    </script>
</body>
</html>