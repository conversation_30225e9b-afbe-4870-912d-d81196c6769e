package com.lirong.personnel.controller;

import java.util.List;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.lirong.common.annotation.Log;
import com.lirong.common.enums.BusinessType;
import com.lirong.personnel.domain.IdwPeopleTraining;
import com.lirong.personnel.service.IdwPeopleTrainingService;
import com.lirong.common.core.controller.BaseController;
import com.lirong.common.core.domain.AjaxResult;
import com.lirong.common.utils.poi.ExcelUtil;
import com.lirong.common.core.page.TableDataInfo;

/**
 * 培养情况Controller
 *
 * <AUTHOR>
 * @date 2021-06-07
 */
@Controller
@RequestMapping("/people/training")
public class IdwPeopleTrainingController extends BaseController {
    private String prefix = "personnel/training";

    @Autowired
    private IdwPeopleTrainingService idwPeopleTrainingService;

    @RequiresPermissions("people:training:view")
    @GetMapping("/{peopleCode}")
    public String training(@PathVariable("peopleCode") String peopleCode, ModelMap mmap) {
        mmap.put("peopleCode", peopleCode);
        return prefix + "/training";
    }

    /**
     * 查询培养情况列表
     */
    @RequiresPermissions("people:training:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(IdwPeopleTraining idwPeopleTraining) {
        startPage();
        List<IdwPeopleTraining> list = idwPeopleTrainingService.selectIdwPeopleTrainingList(idwPeopleTraining);
        return getDataTable(list);
    }

    /**
     * 新增培养情况
     */
    @GetMapping("/add/{peopleCode}")
    public String add(@PathVariable("peopleCode") String peopleCode, ModelMap mmap) {
        mmap.put("peopleCode", peopleCode);
        return prefix + "/add";
    }

    /**
     * 新增保存培养情况
     */
    @RequiresPermissions("people:training:add")
    @Log(title = "人员培养情况", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(IdwPeopleTraining idwPeopleTraining) {
        return toAjax(idwPeopleTrainingService.insertIdwPeopleTraining(idwPeopleTraining));
    }

    /**
     * 修改培养情况
     */
    @GetMapping("/edit/{trainingId}")
    public String edit(@PathVariable("trainingId") Long trainingId, ModelMap mmap) {
        IdwPeopleTraining idwPeopleTraining = idwPeopleTrainingService.selectIdwPeopleTrainingById(trainingId);
        mmap.put("idwPeopleTraining", idwPeopleTraining);
        return prefix + "/edit";
    }

    /**
     * 修改保存培养情况
     */
    @RequiresPermissions("people:training:edit")
    @Log(title = "人员培养情况", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(IdwPeopleTraining idwPeopleTraining) {
        return toAjax(idwPeopleTrainingService.updateIdwPeopleTraining(idwPeopleTraining));
    }

    /**
     * 删除培养情况
     */
    @RequiresPermissions("people:training:remove")
    @Log(title = "人员培养情况", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(idwPeopleTrainingService.deleteIdwPeopleTrainingByIds(ids));
    }

    /**
     * 导出培养情况列表
     */
    @RequiresPermissions("people:training:export")
    @Log(title = "人员培养情况", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(IdwPeopleTraining idwPeopleTraining) {
        List<IdwPeopleTraining> list = idwPeopleTrainingService.selectIdwPeopleTrainingList(idwPeopleTraining);
        ExcelUtil<IdwPeopleTraining> util = new ExcelUtil<IdwPeopleTraining>(IdwPeopleTraining.class);
        return util.exportExcel(list, "training");
    }
}
