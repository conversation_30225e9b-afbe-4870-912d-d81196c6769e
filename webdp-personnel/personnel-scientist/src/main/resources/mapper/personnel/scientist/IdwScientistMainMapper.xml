<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lirong.personnel.scientist.mapper.IdwScientistMainMapper">

    <resultMap type="com.lirong.personnel.scientist.domain.IdwScientistMain" id="IdwScientistMainResult">
        <result property="peopleId"    column="people_id"    />
        <result property="peopleCode"    column="people_code"    />
        <result property="country"    column="country"    />
        <result property="category"    column="category"    />
        <result property="nameCn"    column="name_cn"    />
        <result property="nameEn"    column="name_en"    />
        <result property="peopleType"    column="people_type"    />
        <result property="gender"    column="gender"    />
        <result property="birthplace"    column="birthplace"    />
        <result property="birthday"    column="birthday"    />
        <result property="status"    column="status"    />
        <result property="age"    column="age"    />
        <result property="avatar"    column="avatar"    />
        <result property="email"    column="email"    />
        <result property="telephone"    column="telephone"    />
        <result property="graduatedUniversity"    column="graduated_university"    />
        <result property="degree"    column="degree"    />
        <result property="orgCode"    column="org_code"    />
        <result property="orgName"    column="org_name"    />
        <result property="post"    column="post"    />
        <result property="appointmentDate"    column="appointment_date"    />
        <result property="occupation"    column="occupation"    />
        <result property="workplace"    column="workplace"    />
        <result property="participantOrg"    column="participant_org"    />
        <result property="innerCode"    column="inner_code"    />
        <result property="profileCn"    column="profile_cn"    />
        <result property="profileEn"    column="profile_en"    />
        <result property="peopleCharacter"    column="people_character"    />
        <result property="hobby"    column="hobby"    />
        <result property="education"    column="education"    />
        <result property="strengthsWeaknesses"    column="strengths_weaknesses"    />
        <result property="technicalExpertise"    column="technical_expertise"    />
        <result property="achievement"    column="achievement"    />
        <result property="physicalCondition"    column="physical_condition"    />
        <result property="extraversion"    column="extraversion"    />
        <result property="emotionalStability"    column="emotional_stability"    />
        <result property="agreeableness"    column="agreeableness"    />
        <result property="politicalOrientation"    column="political_orientation"    />
        <result property="conscientiousness"    column="conscientiousness"    />
        <result property="openness"    column="openness"    />
        <result property="attitudeTowardsChina"    column="attitude_towards_china"    />
        <result property="tags"    column="tags"    />
        <result property="orderNum"    column="order_num"    />
        <result property="educationalExperience"    column="educational_experience"    />
        <result property="assignments"    column="assignments"    />
        <result property="source"    column="source"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <!--新增装备领域科学家-->
    <insert id="insertScientist" parameterType="com.lirong.personnel.scientist.domain.IdwScientistMain" useGeneratedKeys="true" keyProperty="peopleId">
        insert into idw_people_main
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="peopleCode != null">people_code,</if>
            <if test="country != null and country != ''">country,</if>
            <if test="peopleType != null and peopleType != ''">people_type,</if>
            <if test="category != null and category != ''">category,</if>
            <if test="nameCn != null and nameCn != ''">name_cn,</if>
            <if test="nameEn != null">name_en,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="gender != null and gender != ''">gender,</if>
            <if test="birthplace != null">birthplace,</if>
            <if test="birthday != null">birthday,</if>
            <if test="age != null">age,</if>
            <if test="avatar != null">avatar,</if>
            <if test="email != null">email,</if>
            <if test="telephone != null">telephone,</if>
            <if test="graduatedUniversity != null">graduated_university,</if>
            <if test="degree != null">degree,</if>
            <if test="education != null">education,</if>
            <if test="orgCode != null">org_code,</if>
            <if test="orgName != null">org_name,</if>
            <if test="post != null">post,</if>
            <if test="appointmentDate != null">appointment_date,</if>
            <if test="occupation != null">occupation,</if>
            <if test="workplace != null">workplace,</if>
            <if test="participantOrg != null">participant_org,</if>
            <if test="innerCode != null">inner_code,</if>
            <if test="profileCn != null">profile_cn,</if>
            <if test="profileEn != null">profile_en,</if>
            <if test="educationalExperience != null">educational_experience,</if>
            <if test="assignments != null">assignments,</if>
            <if test="peopleCharacter != null">people_character,</if>
            <if test="hobby != null">hobby,</if>
            <if test="strengthsWeaknesses != null">strengths_weaknesses,</if>
            <if test="technicalExpertise != null">technical_expertise,</if>
            <if test="achievement != null">achievement,</if>
            <if test="physicalCondition != null">physical_condition,</if>
            <if test="extraversion != null">extraversion,</if>
            <if test="emotionalStability != null">emotional_stability,</if>
            <if test="politicalOrientation != null">political_orientation,</if>
            <if test="agreeableness != null">agreeableness,</if>
            <if test="conscientiousness != null">conscientiousness,</if>
            <if test="openness != null">openness,</if>
            <if test="attitudeTowardsChina != null">attitude_towards_china,</if>
            <if test="orderNum != null">order_num,</if>
            <if test="tags != null">tags,</if>
            <if test="source != null and source != ''">source,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            is_delete
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="peopleCode != null">#{peopleCode},</if>
            <if test="country != null and country != ''">#{country},</if>
            <if test="peopleType != null and peopleType != ''">#{peopleType},</if>
            <if test="category != null and category != ''">#{category},</if>
            <if test="nameCn != null and nameCn != ''">#{nameCn},</if>
            <if test="nameEn != null">#{nameEn},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="gender != null and gender != ''">#{gender},</if>
            <if test="birthplace != null">#{birthplace},</if>
            <if test="birthday != null">#{birthday},</if>
            <if test="age != null">#{age},</if>
            <if test="avatar != null">#{avatar},</if>
            <if test="email != null">#{email},</if>
            <if test="telephone != null">#{telephone},</if>
            <if test="graduatedUniversity != null">#{graduatedUniversity},</if>
            <if test="degree != null">#{degree},</if>
            <if test="education != null">#{education},</if>
            <if test="orgCode != null">#{orgCode},</if>
            <if test="orgName != null">#{orgName},</if>
            <if test="post != null">#{post},</if>
            <if test="appointmentDate != null">#{appointmentDate},</if>
            <if test="occupation != null">#{occupation},</if>
            <if test="workplace != null">#{workplace},</if>
            <if test="participantOrg != null">#{participantOrg},</if>
            <if test="innerCode != null">#{innerCode},</if>
            <if test="profileCn != null">#{profileCn},</if>
            <if test="profileEn != null">#{profileEn},</if>
            <if test="educationalExperience != null">#{educationalExperience},</if>
            <if test="assignments != null">#{assignments},</if>
            <if test="peopleCharacter != null">#{peopleCharacter},</if>
            <if test="hobby != null">#{hobby},</if>
            <if test="strengthsWeaknesses != null">#{strengthsWeaknesses},</if>
            <if test="technicalExpertise != null">#{technicalExpertise},</if>
            <if test="achievement != null">#{achievement},</if>
            <if test="physicalCondition != null">#{physicalCondition},</if>
            <if test="extraversion != null">#{extraversion},</if>
            <if test="emotionalStability != null">#{emotionalStability},</if>
            <if test="politicalOrientation != null">#{politicalOrientation},</if>
            <if test="agreeableness != null">#{agreeableness},</if>
            <if test="conscientiousness != null">#{conscientiousness},</if>
            <if test="openness != null">#{openness},</if>
            <if test="attitudeTowardsChina != null">#{attitudeTowardsChina},</if>
            <if test="orderNum != null">#{orderNum},</if>
            <if test="tags != null">#{tags},</if>
            <if test="source != null and source != ''">#{source},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            0
        </trim>
    </insert>

    <!--根据人员编码更新-->
    <update id="updateScientisByPeopleCode" parameterType="com.lirong.personnel.scientist.domain.IdwScientistMain">
        update idw_people_main
        <trim prefix="SET" suffixOverrides=",">
            <if test="country != null">country = #{country},</if>
            <if test="peopleType != null">people_type = #{peopleType},</if>
            <if test="category != null">category = #{category},</if>
            <if test="nameCn != null">name_cn = #{nameCn},</if>
            <if test="nameEn != null">name_en = #{nameEn},</if>
            <if test="status != null">status = #{status},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="birthplace != null">birthplace = #{birthplace},</if>
            <if test="birthday != null">birthday = #{birthday},</if>
            <if test="age != null">age = #{age},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="email != null">email = #{email},</if>
            <if test="telephone != null">telephone = #{telephone},</if>
            <if test="graduatedUniversity != null">graduated_university = #{graduatedUniversity},</if>
            <if test="degree != null">degree = #{degree},</if>
            <if test="education != null">education = #{education},</if>
            <if test="orgCode != null">org_code = #{orgCode},</if>
            <if test="orgName != null">org_name = #{orgName},</if>
            <if test="post != null">post = #{post},</if>
            <if test="appointmentDate != null">appointment_date = #{appointmentDate},</if>
            <if test="occupation != null">occupation = #{occupation},</if>
            <if test="workplace != null">workplace = #{workplace},</if>
            <if test="participantOrg != null">participant_org = #{participantOrg},</if>
            <if test="innerCode != null">inner_code = #{innerCode},</if>
            <if test="profileCn != null">profile_cn = #{profileCn},</if>
            <if test="profileEn != null">profile_en = #{profileEn},</if>
            <if test="educationalExperience != null">educational_experience = #{educationalExperience},</if>
            <if test="assignments != null">assignments = #{assignments},</if>
            <if test="peopleCharacter != null">people_character = #{peopleCharacter},</if>
            <if test="hobby != null">hobby = #{hobby},</if>
            <if test="strengthsWeaknesses != null">strengths_weaknesses = #{strengthsWeaknesses},</if>
            <if test="technicalExpertise != null">technical_expertise = #{technicalExpertise},</if>
            <if test="achievement != null">achievement = #{achievement},</if>
            <if test="physicalCondition != null">physical_condition = #{physicalCondition},</if>
            <if test="extraversion != null">extraversion = #{extraversion},</if>
            <if test="emotionalStability != null">emotional_stability = #{emotionalStability},</if>
            <if test="politicalOrientation != null">political_orientation = #{politicalOrientation},</if>
            <if test="agreeableness != null">agreeableness = #{agreeableness},</if>
            <if test="conscientiousness != null">conscientiousness = #{conscientiousness},</if>
            <if test="openness != null">openness = #{openness},</if>
            <if test="attitudeTowardsChina != null">attitude_towards_china = #{attitudeTowardsChina},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="tags != null">tags = #{tags},</if>
            <if test="source != null and source != ''">source = #{source},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where people_code = #{peopleCode}
    </update>

    <!--根据人员编码查询-->
    <select id="selectByPeopleCodes" resultMap="IdwScientistMainResult">
        SELECT
            people_id, people_code, country, category,
            name_cn, name_en, people_type, gender,
            birthplace, birthday, status, age,
            tags, email, telephone, graduated_university,
            degree, org_code, org_name, post,
            appointment_date, occupation, workplace, participant_org,
            inner_code, profile_cn, profile_en, people_character,
            hobby, education, strengths_weaknesses, technical_expertise,
            achievement, physical_condition, extraversion, emotional_stability,
            agreeableness, political_orientation, conscientiousness, openness,
            attitude_towards_china, order_num, source, avatar
        FROM
            idw_people_main
        WHERE
            is_delete = 0
            AND category = '装备领域科学家'
        <if test="peopleCodes != null and peopleCodes.length > 0">
            AND people_code IN
            <foreach item="peopleCode" collection="peopleCodes" open="(" separator="," close=")">
                #{peopleCode}
            </foreach>
        </if>
    </select>

</mapper>