package com.lirong.personnel.scientist.service.impl;

import com.lirong.common.config.WebdpConfig;
import com.lirong.common.constant.Constants;
import com.lirong.common.service.FileMatchingService;
import com.lirong.common.utils.*;
import com.lirong.common.utils.file.FileUploadUtils;
import com.lirong.common.utils.file.FileUtils;
import com.lirong.organization.common.domain.IdwOrg;
import com.lirong.organization.common.mapper.IdwOrgMapper;
import com.lirong.personnel.scientist.domain.IdwScientistMain;
import com.lirong.personnel.common.domain.IdwPeopleMain;
import com.lirong.personnel.scientist.mapper.IdwScientistMainMapper;
import com.lirong.personnel.common.mapper.IdwPeopleMainMapper;
import com.lirong.personnel.scientist.service.IdwScientistMainService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.util.*;

/**
 * 装备领域科学家Service业务层处理
 *
 * <AUTHOR>
 * @date 2020-12-25
 */
@Service
@Transactional
public class IdwScientistMainServiceImpl implements IdwScientistMainService {

    @Autowired//人员
    private IdwPeopleMainMapper idwPeopleMainMapper;
    @Autowired//装备领域科学家
    private IdwScientistMainMapper idwScientistMainMapper;
    @Autowired//组织机构
    private IdwOrgMapper idwOrgMapper;
    @Autowired//文件匹配
    private List<FileMatchingService> fileMatchingServices;

    /**
     * 人员相关文件上传保存
     *
     * @param fileName 文件名称
     * @param fileUrl  文件路径
     * @return 结果
     */
    @Override
    public String saveBatchUploadFile(String fileName, String fileUrl) {
        StringBuilder notEqualsFilerName = new StringBuilder();//未匹配的文件名称
        int row = 1;
        if (StringUtils.isNotBlank(fileName) && StringUtils.isNotBlank(fileUrl)) {
            String userName = ShiroUtils.getUserName();
            String[] fileNameArray = fileName.split(":_:_:");
            String[] fileUrlArray = fileUrl.split(":_:_:");
            for (int i = 0; i < fileNameArray.length; i++) {
                String name = fileNameArray[i];
                String url = fileUrlArray[i];
                //校验文件名称是否存在
                int count = idwPeopleMainMapper.selectCountByAvatar(name);
                if (count > 0) {
                    idwPeopleMainMapper.updateAvatarByFileName(name, url, userName);
                } else {
                    int matchingCount = 0;
                    for (FileMatchingService fileMatchingService : fileMatchingServices) {
                        int result = fileMatchingService.accordingFileNameModificationFileUrl("personnel", name, url, userName);
                        if (result > 0) {
                            matchingCount++;
                        }
                    }
                    if (matchingCount == 0) {
                        //根据文件路径删除
                        FileUtils.deleteFile(WebdpConfig.getPath() + url.replace(Constants.RESOURCE_PREFIX, ""));
                        notEqualsFilerName.append("<br/>").append(row).append("、").append(name);
                        row++;
                    }
                }
            }
        }
        //获取没有匹配上的文件名称
        return !notEqualsFilerName.toString().equals("") ? notEqualsFilerName.insert(0, "未匹配的文件名称如下：").toString() : notEqualsFilerName.toString();
    }

    /**
     * 根据人员编码查询
     *
     * @param peopleCodes 人员编码
     * @return 结果
     */
    @Override
    public List<IdwScientistMain> selectByPeopleCodes(String[] peopleCodes) {
        return idwScientistMainMapper.selectByPeopleCodes(peopleCodes);
    }

    /**
     * 导入校验数据格式
     *
     * @param peopleList       数据列表
     * @param filePathIndexMap key 文件在压缩包中的相对路径 value 文件对应在filePathList中的索引
     * @param filePathList     上传后的文件路径
     * @param baseDir          临时文件夹目录
     * @param updateSupport    是否更新
     * @return 结果
     */
    @Override
    public List<String> verifyImportScientist(List<IdwScientistMain> peopleList, Map<String, Integer> filePathIndexMap, List<String> filePathList, String baseDir, boolean updateSupport) {
        if (StringUtils.isNull(peopleList) || peopleList.size() < 1) {
            return null;
        }
        //处理完成数据列表
        List<IdwScientistMain> treatingAfterPeopleList = new ArrayList<>();
        //处理完成人员编码列表
        List<String> treatingAfterPeopleCodeList = new ArrayList<>();
        int row = 1;
        boolean isFailure = false;
        //需要删除文件路径
        List<String> deleteFilePathList = new ArrayList<>();
        //需要拷贝的文件路径
        List<String> temporaryFilePathList = new ArrayList<>();
        //拷贝文件目标路径
        List<String> newFilePathList = new ArrayList<>();
        // 查询当前分类中最大的排序号
        int maxOrderNum = idwPeopleMainMapper.selectMaxOrderNum("装备领域科学家");
        //获取当前系统年份
        Calendar date = Calendar.getInstance();
        int year = date.get(Calendar.YEAR);
        List<String> msgList = new ArrayList<>();
        String userName = ShiroUtils.getUserName();
        for (IdwScientistMain people : peopleList) {
            row++;
            if (StringUtils.isNotNull(people)) {
                //赋值人员类型
                people.setCategory("装备领域科学家");
                //格式化任职日期
                String appointmentDate = people.getAppointmentDate();
                if (StringUtils.isNotBlank(appointmentDate)) {
                    people.setAppointmentDate(DateUtils.updateDateSeparator(appointmentDate));
                    if (!DateUtils.isDate(appointmentDate)) {
                        isFailure = true;
                        msgList.add("装备领域科学家,第" + row + "行," + " 任职日期（" + appointmentDate + "）格式错误，格式为：" + DateUtils.YYYY_MM_DD);
                    }
                }
                //格式化出生日期
                String birthday = people.getBirthday();
                if (StringUtils.isNotBlank(birthday)) {
                    people.setBirthday(DateUtils.updateDateSeparator(birthday));
                    if (!DateUtils.isDate(birthday)) {
                        isFailure = true;
                        msgList.add("装备领域科学家,第" + row + "行," + " 出生日期（" + birthday + "）格式错误，格式为：" + DateUtils.YYYY_MM_DD);
                    } else {
                        // 根据出生日期计算年龄
                        String inputYear = people.getBirthday().split("-")[0];
                        people.setAge(year - Integer.parseInt(inputYear));
                    }
                }

                if (StringUtils.isBlank(people.getPeopleCode())) {
                    isFailure = true;
                    msgList.add("装备领域科学家,第" + row + "行," + " 人员编码为空");
                }
                if (StringUtils.isBlank(people.getCountry())) {
                    isFailure = true;
                    msgList.add("装备领域科学家,第" + row + "行," + " 国家为空或不存在");
                }
                if (StringUtils.isBlank(people.getNameCn())) {
                    isFailure = true;
                    msgList.add("装备领域科学家,第" + row + "行," + " 中文名称为空");
                } else if (StringUtils.isBlank(people.getTags())) {
                    //赋值标签
                    if (StringUtils.isBlank(people.getNameEn()) || people.getNameCn().equals(people.getNameEn())) {
                        people.setTags(people.getNameCn());
                    } else {
                        people.setTags(people.getNameCn() + "," + people.getNameEn());
                    }
                }
                if (StringUtils.isBlank(people.getGender())) {
                    isFailure = true;
                    msgList.add("装备领域科学家,第" + row + "行," + " 性别为空或不存在（男/女/未知）");
                }
                if (StringUtils.isBlank(people.getPeopleType())) {
                    //人员类型为空 默认其他
                    people.setPeopleType(DictUtils.getDictValue("personnel_scientist_type", "其他"));
                }
                if (StringUtils.isBlank(people.getStatus())) {
                    //工作状态为空 默认 现职
                    people.setStatus(DictUtils.getDictValue("sys_work_status", "现职"));
                }
                if (StringUtils.isBlank(people.getSource())) {
                    isFailure = true;
                    msgList.add("装备领域科学家,第" + row + "行," + " 数据来源为空");
                } else {
                    //格式化数据来源
                    people.setSource(people.getSource().replaceAll("[\\t\\n\\r]", ";").replaceAll("[；，,、]", ";"));
                }
                //校验所在机构编码是否存在
                if (StringUtils.isNotBlank(people.getOrgCode())) {
                    IdwOrg org = idwOrgMapper.selectOrgByOrgCode(people.getOrgCode());
                    if (StringUtils.isNotNull(org)) {
                        people.setOrgName(org.getOrgNameCn());
                    } else {
                        isFailure = true;
                        msgList.add("装备领域科学家,第" + row + "行," + " 所在机构编码不存在");
                    }
                } else if (StringUtils.isNotBlank(people.getOrgName())) {
                    List<IdwOrg> orgList = idwOrgMapper.selectOrgByOrgName(people.getOrgName(), null);
                    if (StringUtils.isNotNull(orgList) && orgList.size() == 1) {
                        people.setOrgCode(orgList.get(0).getOrgCode());
                    }
                }
                if (StringUtils.isNull(people.getOrderNum())) {
                    //排序号为空时赋值
                    maxOrderNum++;
                    people.setOrderNum(maxOrderNum);
                }
                //校验数据是否存在
                IdwPeopleMain oldPeople = idwPeopleMainMapper.selectPeopleByPeopleCode(people.getPeopleCode());
                if (StringUtils.isNotNull(oldPeople)) {
                    //数据存在 赋值ID
                    people.setPeopleId(oldPeople.getPeopleId());
                }

                //校验图片
                String originalAvatar = people.getAvatar();
                if (StringUtils.isNotBlank(originalAvatar)) {
                    if (originalAvatar.contains(Constants.RESOURCE_PREFIX)) {
                        String avatarFilePath = originalAvatar.replace(Constants.RESOURCE_PREFIX, WebdpConfig.getProfile());
                        if (!new File(avatarFilePath).exists()) {
                            isFailure = true;
                            msgList.add("装备领域科学家,第" + row + "行," + " 头像（" + originalAvatar + "）不存在");
                        }
                    } else {
                        //替换文件名称中的特殊字符
                        originalAvatar = FileUploadUtils.replaceFileNameSpecialCharacter(originalAvatar).toLowerCase();
                        if (StringUtils.isNotBlank(originalAvatar)) {
                            if (StringUtils.isNotNull(filePathIndexMap) && filePathIndexMap.size() > 0) {
                                Integer index = filePathIndexMap.get(originalAvatar);
                                //不存在 匹配文件名称
                                if (StringUtils.isNull(index)) {
                                    String excelFileName = StringUtils.getFileName(originalAvatar);
                                    //使用文件名称匹配
                                    for (String key : filePathIndexMap.keySet()) {
                                        String fileName = StringUtils.getFileName(key);
                                        if (excelFileName.equals(fileName)) {
                                            index = filePathIndexMap.get(key);
                                            break;
                                        }
                                    }
                                }
                                if (StringUtils.isNotNull(index)) {
                                    //当数据存在但是支持更新或数据不存在时处理图片
                                    if ((StringUtils.isNotNull(oldPeople) && updateSupport) || StringUtils.isNull(oldPeople)) {
                                        //保存需要删除的图片路径
                                        if (StringUtils.isNotNull(oldPeople) && StringUtils.isNotBlank(oldPeople.getAvatar())) {
                                            deleteFilePathList.add(oldPeople.getAvatar());
                                        }
                                        //获取文件名称
                                        String temporaryFilePath = filePathList.get(index);
                                        String[] filePathArr = temporaryFilePath.split("/");
                                        //根据文件名称与纬度路径生成新的文件路径
                                        String newFilePath = FileUploadUtils.generateFilePath(WebdpConfig.getPersonnelPath(), filePathArr[filePathArr.length - 1]);
                                        //保存文件路径
                                        temporaryFilePathList.add(temporaryFilePath);
                                        //保存新的文件路径
                                        newFilePathList.add(newFilePath);
                                        //赋值新图片路径
                                        people.setAvatar(newFilePath);
                                    }
                                } else {
                                    isFailure = true;
                                    msgList.add("装备领域科学家,第" + row + "行," + " 头像（" + originalAvatar + "）不存在");
                                }
                            } else {
                                isFailure = true;
                                msgList.add("装备领域科学家,第" + row + "行," + " 头像不为空且压缩包文件为空");
                            }
                        }
                    }
                }
                treatingAfterPeopleCodeList.add(people.getPeopleCode());
                treatingAfterPeopleList.add(people);
            }
        }
        CacheUtils.put("peopleImportTreatingAfterPeopleCodeList-" + userName, treatingAfterPeopleCodeList);
        CacheUtils.put("peopleImportTreatingDeleteFilePathList-" + userName, deleteFilePathList);
        CacheUtils.put("peopleImportTreatingTemporaryFilePathList-" + userName, temporaryFilePathList);
        CacheUtils.put("peopleImportTreatingNewFilePathList-" + userName, newFilePathList);
        if (isFailure) {
            return msgList;
        } else {
            //数据列表
            CacheUtils.put("peopleImportTreatingAfterPeopleList-" + userName, treatingAfterPeopleList);
        }
        return null;
    }

    /**
     * 导入装备领域科学家数据
     *
     * @param updateSupport 是否支持更新, 如果已存在, 则进行更新
     * @param operName      操作用户
     * @return 结果
     */
    @Override
    public String importScientist(boolean updateSupport, String operName) {
        Date nowDate = DateUtils.getNowDate();
        long insertCount = 0;
        long updateCount = 0;
        List<IdwScientistMain> peopleList = (List<IdwScientistMain>) CacheUtils.get("peopleImportTreatingAfterPeopleList-" + operName);
        if (StringUtils.isNull(peopleList) || peopleList.size() < 1) {
            return null;
        }
        for (IdwScientistMain people : peopleList) {
            if (StringUtils.isNotNull(people)) {
                if (StringUtils.isNull(people.getPeopleId())) {
                    //新增
                    insertCount++;
                    people.setCreateBy(operName);
                    people.setCreateTime(nowDate);
                    idwScientistMainMapper.insertScientist(people);
                } else if (updateSupport) {
                    //修改
                    updateCount++;
                    people.setUpdateBy(operName);
                    people.setUpdateTime(nowDate);
                    idwScientistMainMapper.updateScientisByPeopleCode(people);
                }
            }
        }
        //清除装备领域科学家数据列表缓存
        CacheUtils.remove("peopleImportTreatingAfterPeopleList-" + operName);
        return "装备领域科学家共：" + peopleList.size() + "条" + ",新增：" + insertCount + "条" + ",修改：" + updateCount + "条";
    }
}
