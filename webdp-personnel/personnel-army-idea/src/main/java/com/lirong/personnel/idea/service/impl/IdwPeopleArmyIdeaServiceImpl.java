package com.lirong.personnel.idea.service.impl;

import java.util.*;

import com.lirong.common.utils.CacheUtils;
import com.lirong.common.utils.DateUtils;
import com.lirong.common.utils.ShiroUtils;
import com.lirong.common.utils.StringUtils;
import com.lirong.personnel.common.domain.IdwPeopleMain;
import com.lirong.personnel.common.service.IdwPeopleMainService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lirong.personnel.idea.mapper.IdwPeopleArmyIdeaMapper;
import com.lirong.personnel.idea.domain.IdwPeopleArmyIdea;
import com.lirong.personnel.idea.service.IdwPeopleArmyIdeaService;
import com.lirong.common.core.text.Convert;

/**
 * 治军理念Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-04-06
 */
@Service
public class IdwPeopleArmyIdeaServiceImpl implements IdwPeopleArmyIdeaService {
    @Autowired
    private IdwPeopleArmyIdeaMapper idwPeopleArmyIdeaMapper;
    @Autowired//人员通用
    private IdwPeopleMainService idwPeopleMainService;

    /**
     * 查询治军理念
     *
     * @param ideaId 治军理念ID
     * @return 治军理念
     */
    @Override
    public IdwPeopleArmyIdea selectIdwPeopleArmyIdeaById(Long ideaId) {
        return idwPeopleArmyIdeaMapper.selectIdwPeopleArmyIdeaById(ideaId);
    }

    /**
     * 查询治军理念列表
     *
     * @param idwPeopleArmyIdea 治军理念
     * @return 治军理念
     */
    @Override
    public List<IdwPeopleArmyIdea> selectIdwPeopleArmyIdeaList(IdwPeopleArmyIdea idwPeopleArmyIdea) {
        return idwPeopleArmyIdeaMapper.selectIdwPeopleArmyIdeaList(idwPeopleArmyIdea);
    }

    /**
     * 新增治军理念
     *
     * @param idwPeopleArmyIdea 治军理念
     * @return 结果
     */
    @Override
    public int insertIdwPeopleArmyIdea(IdwPeopleArmyIdea idwPeopleArmyIdea) {
        idwPeopleArmyIdea.setCreateBy(ShiroUtils.getUserName());
        idwPeopleArmyIdea.setCreateTime(DateUtils.getNowDate());
        return idwPeopleArmyIdeaMapper.insertIdwPeopleArmyIdea(idwPeopleArmyIdea);
    }

    /**
     * 修改治军理念
     *
     * @param idwPeopleArmyIdea 治军理念
     * @return 结果
     */
    @Override
    public int updateIdwPeopleArmyIdea(IdwPeopleArmyIdea idwPeopleArmyIdea) {
        idwPeopleArmyIdea.setUpdateBy(ShiroUtils.getUserName());
        idwPeopleArmyIdea.setUpdateTime(DateUtils.getNowDate());
        return idwPeopleArmyIdeaMapper.updateIdwPeopleArmyIdea(idwPeopleArmyIdea);
    }

    /**
     * 删除治军理念对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteIdwPeopleArmyIdeaByIds(String ids) {
        String userName = ShiroUtils.getUserName();
        return idwPeopleArmyIdeaMapper.deleteIdwPeopleArmyIdeaByIds(Convert.toStrArray(ids), userName);
    }

    /**
     * 校验Excel导入数据
     *
     * @param armyIdeaList 治军理念集合
     * @return 结果
     */
    @Override
    public List<String> verifyImportArmyIdea(List<IdwPeopleArmyIdea> armyIdeaList) {
        if (StringUtils.isNull(armyIdeaList) || armyIdeaList.size() < 1) {
            return null;
        }
        //处理完成治军理念数据列表
        List<IdwPeopleArmyIdea> treatingAfterPeopleArmyIdeaList = new ArrayList<>();
        int row = 1;
        boolean isFailure = false;
        List<String> msgList = new ArrayList<>();
        String userName = ShiroUtils.getUserName();
        List<String> peopleCodeList = (List<String>) CacheUtils.get("peopleImportTreatingAfterPeopleCodeList-" + userName);
        for (IdwPeopleArmyIdea peopleArmyIdea : armyIdeaList) {
            row++;
            if (peopleArmyIdea != null) {
                if (StringUtils.isBlank(peopleArmyIdea.getPeopleCode())) {
                    isFailure = true;
                    msgList.add("治军理念,第" + row + "行," + " 人员编码为空");
                } else {
                    String peopleCode = peopleArmyIdea.getPeopleCode();
                    IdwPeopleMain people = idwPeopleMainService.selectPeopleByPeopleCode(peopleCode);
                    if (StringUtils.isNull(people) && (StringUtils.isNull(peopleCodeList) || !peopleCodeList.contains(peopleCode))) {
                        isFailure = true;
                        msgList.add("治军理念,第" + row + "行," + " 人员编码（" + peopleCode + "）不存在");
                    }
                }
                if (StringUtils.isBlank(peopleArmyIdea.getIdea())) {
                    isFailure = true;
                    msgList.add("治军理念,第" + row + "行," + " 治军理念为空");
                }
                if (StringUtils.isBlank(peopleArmyIdea.getSource())) {
                    isFailure = true;
                    msgList.add("治军理念,第" + row + "行," + " 数据来源为空");
                }
                //格式化发布日期
                String oldIssueDate = peopleArmyIdea.getIssueDate();
                if (StringUtils.isNotBlank(oldIssueDate)) {
                    String issueDate = DateUtils.updateDateSeparator(oldIssueDate);
                    if (!DateUtils.isDate(issueDate)) {
                        isFailure = true;
                        msgList.add("治军理念,第" + row + "行," + " 发布日期（" + oldIssueDate + "）格式错误，格式为：" + DateUtils.YYYY_MM_DD);
                    }
                    peopleArmyIdea.setIssueDate(issueDate);
                }
                treatingAfterPeopleArmyIdeaList.add(peopleArmyIdea);
            }
        }
        if (isFailure) {
            return msgList;
        } else {
            CacheUtils.put("peopleImportTreatingAfterPeopleArmyIdeaList-" + userName, treatingAfterPeopleArmyIdeaList);
        }
        return null;
    }

    /**
     * 导入治军理念信息
     *
     * @param updateSupport 是否支持更新, 如果已存在, 则进行更新
     * @param operName      操作用户
     * @return 结果
     */
    @Override
    public String importArmyIdea(boolean updateSupport, String operName) {
        Date nowDate = DateUtils.getNowDate();
        long insertCount = 0;
        long updateCount = 0;
        List<IdwPeopleArmyIdea> peopleArmyIdeaList = (List<IdwPeopleArmyIdea>) CacheUtils.get("peopleImportTreatingAfterPeopleArmyIdeaList-" + operName);
        if (StringUtils.isNull(peopleArmyIdeaList) || peopleArmyIdeaList.size() < 1) {
            return null;
        }
        for (IdwPeopleArmyIdea peopleArmyIdea : peopleArmyIdeaList) {
            if (StringUtils.isNotNull(peopleArmyIdea)) {
                //根据人员编码&发布日期&治军理念查询 判断数据是否已存在
                IdwPeopleArmyIdea armyIdea = idwPeopleArmyIdeaMapper.selectByPeopleCodeAndIssueDateAndIdea(peopleArmyIdea.getPeopleCode(), peopleArmyIdea.getIssueDate(), peopleArmyIdea.getIdea());
                if (StringUtils.isNull(armyIdea)) {
                    //新增
                    insertCount++;
                    peopleArmyIdea.setCreateBy(operName);
                    peopleArmyIdea.setCreateTime(nowDate);
                    idwPeopleArmyIdeaMapper.insertIdwPeopleArmyIdea(peopleArmyIdea);
                } else if (updateSupport) {
                    //更新
                    updateCount++;
                    peopleArmyIdea.setIdeaId(armyIdea.getIdeaId());
                    peopleArmyIdea.setUpdateBy(operName);
                    peopleArmyIdea.setUpdateTime(nowDate);
                    idwPeopleArmyIdeaMapper.updateIdwPeopleArmyIdea(peopleArmyIdea);
                }
            }
        }
        CacheUtils.remove("peopleImportTreatingAfterPeopleArmyIdeaList-" + operName);
        return "治军理念共：" + peopleArmyIdeaList.size() + "条" + ",新增：" + insertCount + "条" + ",修改：" + updateCount + "条";
    }

    /**
     * 根据人员编码查询
     *
     * @param peopleCodes 人员编码
     * @return 结果
     */
    @Override
    public List<IdwPeopleArmyIdea> selectByPeopleCodes(String[] peopleCodes) {
        return idwPeopleArmyIdeaMapper.selectByPeopleCodes(peopleCodes);
    }

    /**
     * 根据编码删除
     *
     * @param type       类型
     * @param codes      编码
     * @param loginName  当年登录用户
     * @param deleteTime 删除时间
     * @return 结果
     */
    @Override
    public int deleteByCode(String type, String[] codes, String loginName, String deleteTime) {
        if (type.equals("people")) {
            return idwPeopleArmyIdeaMapper.deletePeopleArmyIdeaByPeopleCodes(codes, loginName, deleteTime);
        }
        return 1;
    }

    /**
     * 统计提示信息
     *
     * @return 模块名称
     */
    @Override
    public String getStatisticalTips() {
        return "治军理念";
    }

    /**
     * 根据人员编码查询数据量
     *
     * @param peopleCode 人员编码
     * @return 结果
     */
    @Override
    public Integer getStatisticalQuantity(String peopleCode) {
        return idwPeopleArmyIdeaMapper.selectCountByPeopleCode(peopleCode);
    }
}