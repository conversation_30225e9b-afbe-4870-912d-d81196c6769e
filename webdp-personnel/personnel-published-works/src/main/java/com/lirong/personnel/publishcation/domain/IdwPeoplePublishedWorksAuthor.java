package com.lirong.personnel.publishcation.domain;

import com.lirong.common.annotation.Excel;
import com.lirong.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 发表作品作者对象 idw_people_published_works_author
 *
 * <AUTHOR>
 * @date 2021-01-11
 */
public class IdwPeoplePublishedWorksAuthor extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 发表作品作者ID
     */
    private Long authorId;

    /**
     * 发表作品ID
     */
    @Excel(name = "发表作品ID")
    private Long worksId;

    /**
     * 人员编码
     */
    @Excel(name = "人员编码")
    private String peopleCode;

    /**
     * 姓名
     */
    @Excel(name = "姓名")
    private String peopleName;

    /**
     * 头像
     */
    @Excel(name = "头像")
    private String avatar;

    /**
     * 是否删除，0-未删除，1-已删除
     */
    private Integer isDelete;

    public void setAuthorId(Long authorId) {
        this.authorId = authorId;
    }

    public Long getAuthorId() {
        return authorId;
    }

    public void setWorksId(Long worksId) {
        this.worksId = worksId;
    }

    public Long getWorksId() {
        return worksId;
    }

    public void setPeopleCode(String peopleCode) {
        this.peopleCode = peopleCode;
    }

    public String getPeopleCode() {
        return peopleCode;
    }

    public void setPeopleName(String peopleName) {
        this.peopleName = peopleName;
    }

    public String getPeopleName() {
        return peopleName;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("authorId", getAuthorId())
                .append("worksId", getWorksId())
                .append("peopleCode", getPeopleCode())
                .append("peopleName", getPeopleName())
                .append("avatar", getAvatar())
                .append("isDelete", getIsDelete())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
