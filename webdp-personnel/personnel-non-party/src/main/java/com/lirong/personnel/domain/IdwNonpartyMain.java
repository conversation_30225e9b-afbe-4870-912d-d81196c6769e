package com.lirong.personnel.domain;

import com.lirong.common.annotation.Excel;
import com.lirong.common.core.domain.BaseEntity;

import com.lirong.common.utils.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 党外人士对象 idw_people_main
 *
 * <AUTHOR>
 * @date 2021-06-07
 */
public class IdwNonpartyMain extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 人员ID
     */
    private Long peopleId;

    /**
     * 人员编码
     */
    @Excel(name = "人员编码")
    private String peopleCode;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 国家
     */
    @Excel(name = "国家", dictType = "sys_country")
    private String country;

    /**
     * 人物分类
     */
    private String category;

    /**
     * 中文名称
     */
    @Excel(name = "姓名")
    private String nameCn;

    /**
     * 曾用名
     */
    @Excel(name = "曾用名")
    private String formerName;

    /**
     * 性别
     */
    @Excel(name = "性别", dictType = "sys_user_sex", combo = {"男", "女", "未知"})
    private String gender;

    /**
     * 证件类型
     */
    @Excel(name = "证件类型")
    private String certificateType;

    /**
     * 证件号码
     */
    @Excel(name = "证件号码")
    private String idNumber;

    /**
     * 办公电话
     */
    @Excel(name = "办公电话")
    private String officePhone;

    /**
     * 家庭电话
     */
    @Excel(name = "家庭电话")
    private String homePhone;

    /**
     * 传真号码
     */
    @Excel(name = "传真")
    private String faxNumber;

    /**
     * 通讯地址
     */
    @Excel(name = "通讯地址")
    private String address;

    /**
     * 邮政编码
     */
    @Excel(name = "邮政编码")
    private String zipCode;

    /**
     * 民族
     */
    @Excel(name = "民族", dictType = "personnel_nation")
    private String nationality;

    /**
     * 籍贯
     */
    @Excel(name = "籍贯")
    private String nativePlace;

    /**
     * 出生地
     */
    @Excel(name = "出生地")
    private String birthplace;

    /**
     * 出生日期
     */
    @Excel(name = "出生日期")
    private String birthday;

    /**
     * 参加工作年月
     */
    @Excel(name = "参加工作年月")
    private String workingDate;

    /**
     * 年龄
     */
    @Excel(name = "年龄")
    private Integer age;

    /**
     * 邮箱
     */
    @Excel(name = "邮箱")
    private String email;

    /**
     * 联系方式
     */
    @Excel(name = "手机号码|联系方式")
    private String telephone;

    /**
     * 全日制毕业院校及专业
     */
    @Excel(name = "全日制毕业院校及专业")
    private String graduatedUniversity;

    /**
     * 全日制教育学历
     */
    @Excel(name = "全日制教育学历")
    private String education;

    /**
     * 全日制学位
     */
    @Excel(name = "全日制学位")
    private String degree;

    /**
     * 在职教育毕业院校及专业
     */
    @Excel(name = "在职教育毕业院校及专业")
    private String inServiceEduUniversity;

    /**
     * 在职教育学历
     */
    @Excel(name = "在职教育学历")
    private String inServiceEduEducation;

    /**
     * 在职教育学位
     */
    @Excel(name = "在职学位")
    private String inServiceEduDegree;

    /**
     * 工作单位
     */
    @Excel(name = "工作单位")
    private String orgName;

    /**
     * 当前职务
     */
    @Excel(name = "当前职务")
    private String post;

    /**
     * 任职日期
     */
    @Excel(name = "任职日期")
    private String appointmentDate;

    /**
     * 人大政协等职务
     */
    @Excel(name = "人大政协等职务")
    private String cppccPosts;

    /**
     * 主要社会职务
     */
    @Excel(name = "主要社会职务")
    private String mainSocialPost;

    /**
     * 专业技术职务
     */
    @Excel(name = "专业技术职务")
    private String technicalPost;

    /**
     * 熟悉专业有何特长
     */
    @Excel(name = "熟悉专业有何特长")
    private String technicalExpertise;

    /**
     * 工作地点
     */
    @Excel(name = "工作地点")
    private String workplace;

    /**
     * 党派
     */
    @Excel(name = "党派")
    private String party;

    /**
     * 加入年月
     */
    @Excel(name = "加入年月")
    private String joinPartyDate;

    /**
     * 第二党派
     */
    @Excel(name = "第二党派")
    private String secondParty;

    /**
     * 加入第二党派年月
     */
    @Excel(name = "第二党派加入年月")
    private String joinSecondPartyDate;

    /**
     * 政治表现
     */
    @Excel(name = "政治表现")
    private String politicalPerformance;

    /**
     * 主要成就
     */
    @Excel(name = "主要成就")
    private String achievement;

    /**
     * 社会影响
     */
    @Excel(name = "社会影响")
    private String socialInfluence;

    /**
     * 身份类别
     */
    @Excel(name = "身份类别")
    private String identityCategory;

    /**
     * 1213层次
     */
    @Excel(name = "1213层次")
    private String level1213;

    /**
     * 排序号
     */
    @Excel(name = "排序号")
    private Integer orderNum;

    /**
     * 标签
     */
    @Excel(name = "标签")
    private String tags;

    /**
     * 数据来源
     */
    @Excel(name = "数据来源")
    private String source;

    /**
     * 是否删除，0-未删除，1-已删除
     */
    private Integer isDelete;

    public void setPeopleId(Long peopleId) {
        this.peopleId = peopleId;
    }

    public Long getPeopleId() {
        return peopleId;
    }

    public void setPeopleCode(String peopleCode) {
        this.peopleCode = peopleCode;
    }

    public String getPeopleCode() {
        return peopleCode;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getCountry() {
        return StringUtils.isNotBlank(country) ? country.trim() : country;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getCategory() {
        return category;
    }

    public void setNameCn(String nameCn) {
        this.nameCn = nameCn;
    }

    public String getNameCn() {
        if (StringUtils.isNotBlank(nameCn)) {
            return nameCn.trim();
        } else {
            return nameCn;
        }
    }

    public String getFormerName() {
        return formerName;
    }

    public void setFormerName(String formerName) {
        this.formerName = formerName;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getGender() {
        return gender;
    }

    public void setCertificateType(String certificateType) {
        this.certificateType = certificateType;
    }

    public String getCertificateType() {
        return certificateType;
    }

    public void setIdNumber(String idNumber) {
        this.idNumber = idNumber;
    }

    public String getIdNumber() {
        return idNumber;
    }

    public String getOfficePhone() {
        return officePhone;
    }

    public void setOfficePhone(String officePhone) {
        this.officePhone = officePhone;
    }

    public void setHomePhone(String homePhone) {
        this.homePhone = homePhone;
    }

    public String getHomePhone() {
        return homePhone;
    }

    public void setFaxNumber(String faxNumber) {
        this.faxNumber = faxNumber;
    }

    public String getFaxNumber() {
        return faxNumber;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getAddress() {
        return address;
    }

    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }

    public String getZipCode() {
        return zipCode;
    }

    public void setNationality(String nationality) {
        this.nationality = nationality;
    }

    public String getNationality() {
        return nationality;
    }

    public void setNativePlace(String nativePlace) {
        this.nativePlace = nativePlace;
    }

    public String getNativePlace() {
        return nativePlace;
    }

    public void setBirthplace(String birthplace) {
        this.birthplace = birthplace;
    }

    public String getBirthplace() {
        return birthplace;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getBirthday() {
        return birthday;
    }

    public String getWorkingDate() {
        return workingDate;
    }

    public void setWorkingDate(String workingDate) {
        this.workingDate = workingDate;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public Integer getAge() {
        return age;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getEmail() {
        return email;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setGraduatedUniversity(String graduatedUniversity) {
        this.graduatedUniversity = graduatedUniversity;
    }

    public String getGraduatedUniversity() {
        return graduatedUniversity;
    }

    public void setDegree(String degree) {
        this.degree = degree;
    }

    public String getDegree() {
        return degree;
    }

    public void setEducation(String education) {
        this.education = education;
    }

    public String getEducation() {
        return education;
    }

    public void setInServiceEduUniversity(String inServiceEduUniversity) {
        this.inServiceEduUniversity = inServiceEduUniversity;
    }

    public String getInServiceEduUniversity() {
        return inServiceEduUniversity;
    }

    public void setInServiceEduEducation(String inServiceEduEducation) {
        this.inServiceEduEducation = inServiceEduEducation;
    }

    public String getInServiceEduEducation() {
        return inServiceEduEducation;
    }

    public void setInServiceEduDegree(String inServiceEduDegree) {
        this.inServiceEduDegree = inServiceEduDegree;
    }

    public String getInServiceEduDegree() {
        return inServiceEduDegree;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setPost(String post) {
        this.post = post;
    }

    public String getPost() {
        return post;
    }

    public void setAppointmentDate(String appointmentDate) {
        this.appointmentDate = appointmentDate;
    }

    public String getAppointmentDate() {
        return appointmentDate;
    }

    public void setWorkplace(String workplace) {
        this.workplace = workplace;
    }

    public String getWorkplace() {
        return workplace;
    }

    public void setCppccPosts(String cppccPosts) {
        this.cppccPosts = cppccPosts;
    }

    public String getCppccPosts() {
        return cppccPosts;
    }

    public void setMainSocialPost(String mainSocialPost) {
        this.mainSocialPost = mainSocialPost;
    }

    public String getMainSocialPost() {
        return mainSocialPost;
    }

    public void setTechnicalPost(String technicalPost) {
        this.technicalPost = technicalPost;
    }

    public String getTechnicalPost() {
        return technicalPost;
    }

    public void setParty(String party) {
        this.party = party;
    }

    public String getParty() {
        return party;
    }

    public void setJoinPartyDate(String joinPartyDate) {
        this.joinPartyDate = joinPartyDate;
    }

    public String getJoinPartyDate() {
        return joinPartyDate;
    }

    public void setSecondParty(String secondParty) {
        this.secondParty = secondParty;
    }

    public String getSecondParty() {
        return secondParty;
    }

    public void setJoinSecondPartyDate(String joinSecondPartyDate) {
        this.joinSecondPartyDate = joinSecondPartyDate;
    }

    public String getJoinSecondPartyDate() {
        return joinSecondPartyDate;
    }

    public void setPoliticalPerformance(String politicalPerformance) {
        this.politicalPerformance = politicalPerformance;
    }

    public String getPoliticalPerformance() {
        return politicalPerformance;
    }

    public void setTechnicalExpertise(String technicalExpertise) {
        this.technicalExpertise = technicalExpertise;
    }

    public String getTechnicalExpertise() {
        return technicalExpertise;
    }

    public void setAchievement(String achievement) {
        this.achievement = achievement;
    }

    public String getAchievement() {
        return achievement;
    }

    public void setSocialInfluence(String socialInfluence) {
        this.socialInfluence = socialInfluence;
    }

    public String getSocialInfluence() {
        return socialInfluence;
    }

    public void setIdentityCategory(String identityCategory) {
        this.identityCategory = identityCategory;
    }

    public String getIdentityCategory() {
        return identityCategory;
    }

    public void setLevel1213(String level1213) {
        this.level1213 = level1213;
    }

    public String getLevel1213() {
        return level1213;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public String getTags() {
        return tags;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getSource() {
        return StringUtils.isNotBlank(source) ? source.replaceAll("；", ";").replaceAll("(\r\n|\r|\n|\n\r)", ";") : source;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("peopleId", getPeopleId())
                .append("peopleCode", getPeopleCode())
                .append("country", getCountry())
                .append("category", getCategory())
                .append("nameCn", getNameCn())
                .append("formerName", getFormerName())
                .append("gender", getGender())
                .append("certificateType", getCertificateType())
                .append("idNumber", getIdNumber())
                .append("officePhone", getOfficePhone())
                .append("homePhone", getHomePhone())
                .append("faxNumber", getFaxNumber())
                .append("address", getAddress())
                .append("zipCode", getZipCode())
                .append("nationality", getNationality())
                .append("nativePlace", getNativePlace())
                .append("birthplace", getBirthplace())
                .append("birthday", getBirthday())
                .append("workingDate", getWorkingDate())
                .append("age", getAge())
                .append("avatar", getAvatar())
                .append("email", getEmail())
                .append("telephone", getTelephone())
                .append("graduatedUniversity", getGraduatedUniversity())
                .append("degree", getDegree())
                .append("education", getEducation())
                .append("inServiceEduUniversity", getInServiceEduUniversity())
                .append("inServiceEduEducation", getInServiceEduEducation())
                .append("inServiceEduDegree", getInServiceEduDegree())
                .append("orgName", getOrgName())
                .append("post", getPost())
                .append("appointmentDate", getAppointmentDate())
                .append("workplace", getWorkplace())
                .append("cppccPosts", getCppccPosts())
                .append("mainSocialPost", getMainSocialPost())
                .append("technicalPost", getTechnicalPost())
                .append("party", getParty())
                .append("joinPartyDate", getJoinPartyDate())
                .append("secondParty", getSecondParty())
                .append("joinSecondPartyDate", getJoinSecondPartyDate())
                .append("politicalPerformance", getPoliticalPerformance())
                .append("technicalExpertise", getTechnicalExpertise())
                .append("achievement", getAchievement())
                .append("socialInfluence", getSocialInfluence())
                .append("identityCategory", getIdentityCategory())
                .append("level1213", getLevel1213())
                .append("orderNum", getOrderNum())
                .append("tags", getTags())
                .append("source", getSource())
                .append("isDelete", getIsDelete())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
