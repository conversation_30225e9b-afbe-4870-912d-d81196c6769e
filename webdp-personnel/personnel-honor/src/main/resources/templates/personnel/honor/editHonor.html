<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改人员荣誉奖项')" />
    <th:block th:include="include :: select2-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-honor-edit" th:object="${idwPeopleHonor}">
            <input name="honorId" th:field="*{honorId}" type="hidden">
            <input name="peopleCode" th:field="*{peopleCode}" type="hidden">
            <div class="form-group">
                <label class="col-sm-3 control-label">荣誉类型：</label>
                <div class="col-sm-8">
                    <select name="honorType" id="honorType" class="form-control" th:with="type=${@dict.getType('personnel_honor_type')}">
                        <option value="" style="color: #b6b6b6" disabled selected>选择荣誉类型</option>
                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{honorType}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">荣誉名称：</label>
                <div class="col-sm-8">
                    <input name="title" th:field="*{title}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group" id="issuedToHtml" style="display:none;">
                <label class="col-sm-3 control-label">颁发对象：</label>
                <div class="col-sm-8">
                    <input name="issuedTo" id="issuedTo" th:field="*{issuedTo}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">颁发机构：</label>
                <div class="col-sm-8">
                    <input name="issuer" th:field="*{issuer}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">颁发日期：</label>
                <div class="col-sm-8">
                    <div class="row">
                        <div class="col-sm-4">
                            <select  name="year" id="year" class="form-control">
                                <option value="">年份</option>
                            </select>
                        </div>
                        <div class="col-sm-4">
                            <select  name="month" id="month" class="form-control">
                                <option value="">月份</option>
                            </select>
                        </div>
                        <div class="col-sm-4">
                            <select  name="day" id="day" class="form-control">
                                <option value="">日</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">描述：</label>
                <div class="col-sm-8">
                    <textarea name="description" class="form-control" rows="8">[[*{description}]]</textarea>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">数据来源：</label>
                <div class="col-sm-8">
                    <input name="source" th:field="*{source}" class="form-control" required>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: select2-js" />
    <script th:inline="javascript">

        let year = document.getElementById("year");
        let date = new Date();
        let selectYear = date.getFullYear();
        //组建年份选择器
        for (let i = selectYear; i >= selectYear - 110; i--) {
            year.options.add(new Option(i, i));
        }

        //组建月份选择器
        let month = document.getElementById("month");
        for (let j = 1; j <= 12; j++) {
            if (j < 10){
                month.options.add(new Option('0'+j, '0'+j));
            }else{
                month.options.add(new Option(j, j));
            }
        }

        //组建日选择器
        let day = document.getElementById("day");
        for (let j = 1; j <= 31; j++) {
            if (j < 10){
                day.options.add(new Option('0'+j, '0'+j));
            }else{
                day.options.add(new Option(j, j));
            }
        }

        let defaultYear = [[${idwPeopleHonor.year}]] ? [[${idwPeopleHonor.year}]] : '';
        let defaultMonth = [[${idwPeopleHonor.month}]] ? [[${idwPeopleHonor.month}]] : '';
        let defaultDay = [[${idwPeopleHonor.day}]] ? [[${idwPeopleHonor.day}]] : '';
        $('#year').val(defaultYear);
        $('#month').val(defaultMonth);
        $('#day').val(defaultDay);
        if (defaultYear == '' || defaultYear == null){
            $('#month').attr("disabled",true);
        }
        if (defaultMonth == '' || defaultMonth == null){
            $('#day').attr("disabled",true);
        }

        $("#year").change(function(){
            let year = $('#year option:selected') .val();
            if (year != '' && year != null){
                $('#month').attr("disabled",false);
            }else{
                $('#month').attr("disabled",true);
                $("#month").select2("val", [""]);
            }
        });

        $("#month").change(function(){
            let month = $('#month option:selected') .val();
            if (month != '' && month != null){
                $('#day').attr("disabled",false);
            }else{
                $("#day").select2("val", [""]);
                $('#day').attr("disabled",true);
            }
        });

        //honorType 为集体荣誉时 显示颁发对象
        $("#honorType").change(function(){
            //要触发的事件
            let honorType = $("#honorType option:selected").val();  //选中的值
            if (honorType == '集体荣誉'){
                document.getElementById("issuedToHtml").style.display="";//显示
            } else {
                document.getElementById("issuedToHtml").style.display="none";//隐藏
                $("#issuedTo").val('');
            }
        });

        //加载页面时判断是否显示颁发对象
        let honorType = $("#honorType option:selected").val();
        if (honorType == '集体荣誉'){
            document.getElementById("issuedToHtml").style.display="";//显示
        }

        let prefix = ctx + "people/honor";
        $("#form-honor-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/editHonor", $('#form-honor-edit').serialize());
            }
        }
    </script>
</body>
</html>