
<div class="container-div">
    <input type="hidden" value="人员教育经历列表">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="form-education">
                <input type="hidden" name="peopleCode" th:value="${peopleCode}">
                <div class="select-list">
                    <ul>
                        <li>
                            <label>毕业院校：</label>
                            <input type="text" name="college"/>
                        </li>
                        <li>
                            <label>专业：</label>
                            <input type="text" name="major"/>
                        </li>
                        <li>
                            <label>学位：</label>
                            <input type="text" name="degree"/>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search('form-education', 'bootstrap-table-education')"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset('form-education', 'bootstrap-table-education')"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar-education" role="group">
            <a class="btn btn-success" onclick="$.operate.add(null, null, 430)" shiro:hasPermission="people:education:add">
                <i class="fa fa-plus"></i> 添加
            </a>
            <a class="btn btn-primary single disabled" onclick="$.operate.edit(null, null, 430)" shiro:hasPermission="people:education:edit">
                <i class="fa fa-edit"></i> 修改
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="people:education:remove">
                <i class="fa fa-remove"></i> 删除
            </a>
        </div>

        <div class="col-sm-12 select-table table-bordered">
            <table id="bootstrap-table-education" data-resizable="true"></table>
        </div>
    </div>
</div>

<script th:inline="javascript">
    let editFlag = [[${@permission.hasPermi('people:education:edit')}]];
    let removeFlag = [[${@permission.hasPermi('people:education:remove')}]];
    let prefix = ctx + "people/education";

    $(function () {
        let educationTableOptions = {
            id: "bootstrap-table-education",//指定表格ID
            toolbar: "toolbar-education",//指定工具栏ID
            formId: "form-education",
            showSearch: false,//隐藏表格搜索框功能
            url: prefix + "/educationList",
            createUrl: prefix + "/addEducation/" + peopleCode,
            updateUrl: prefix + "/editEducation/{id}",
            removeUrl: prefix + "/removeEducation",
            exportUrl: prefix + "/exportEducation",
            importUrl: prefix + "/importEducationData",
            importTemplateUrl: prefix + "/importEducationTemplate",
            sortName: "startDate",
            sortOrder: "Asc",
            modalName: "教育经历",
            uniqueId: "educationId",
            columns: [{
                checkbox: true
            },
            {
                align: 'center',
                title: "序号",
                width: 60,
                formatter: function (value, row, index) {
                    return $.table.serialNumber(index);
                }
            },
            {
                field: 'college',
                title: '毕业院校',
            },
            {
                align: 'center',
                field: 'major',
                title: '专业',
            },
            {
                align: 'center',
                field: 'degree',
                title: '学位',
                width: 120
            },
            {
                align: 'center',
                field: 'startDate',
                title: '开始日期',
                sortable: true,
                width: 120
            },
            {
                align: 'center',
                field: 'endDate',
                title: '结束日期',
                sortable: true,
                width: 120
            },
            {
                title: '操作',
                align: 'center',
                width: 140,
                formatter: function (value, row, index) {
                    let actions = [];
                    actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.educationId + '\', null, 430)"><i class="fa fa-edit"></i>修改</a> ');
                    actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.educationId + '\')"><i class="fa fa-remove"></i>删除</a> ');
                    return actions.join('');
                }
            }]
        };
        $.table.init(educationTableOptions);
    });
</script>
