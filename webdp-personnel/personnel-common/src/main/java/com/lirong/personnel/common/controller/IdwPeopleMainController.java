package com.lirong.personnel.common.controller;

import com.lirong.common.annotation.Log;
import com.lirong.common.core.controller.BaseController;
import com.lirong.common.core.domain.AjaxResult;
import com.lirong.common.core.page.TableDataInfo;
import com.lirong.common.enums.BusinessType;
import com.lirong.common.utils.DictUtils;
import com.lirong.common.utils.ShiroUtils;
import com.lirong.personnel.common.domain.IdwPeopleMain;
import com.lirong.personnel.common.service.IdwPeopleMainService;
import com.lirong.personnel.common.vo.SimplePeople;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.lirong.common.utils.StringUtils;

/**
 * 人员信息Controller
 *
 * <AUTHOR>
 * @date 2020-12-25
 */
@Controller
@RequestMapping("/people/people")
public class IdwPeopleMainController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(IdwPeopleMainController.class);
    private String prefix = "personnel/common";

    @Autowired
    private IdwPeopleMainService idwPeopleMainService;

    /**
     * 跳转人员加工Tabs页
     *
     * @param url 人员请求路径
     */
    @RequiresPermissions("people:people:list")
    @GetMapping("/{url}")
    public String peopleProcessing(@PathVariable("url") String url, ModelMap modelMap) {
        modelMap.put("url", url);
        modelMap.put("category", DictUtils.getDictLabel("sys_people_type", url));
        return prefix + "/peopleList";
    }

    /**
     * 跳转人员数据加工列表
     *
     * @param pageName 待处理/已处理页面名称
     * @param url      人员请求路径
     */
    @RequiresPermissions("people:people:list")
    @GetMapping("list/{pageName}/{url}")
    public String list(@PathVariable("pageName") String pageName, @PathVariable("url") String url, ModelMap modelMap) {
        String category = DictUtils.getDictLabel("sys_category", url);
        modelMap.put("url", url);
        modelMap.put("category", category);
        modelMap.put("assignUserId", ShiroUtils.getUserId());
        return prefix + "/processing/" + pageName;
    }

    /**
     * 跳转人员审核Tabs页
     *
     * @param peopleUrl 人员请求路径
     */
    @RequiresPermissions("people:people:audit")
    @GetMapping("/audit/{peopleUrl}")
    public String audit(@PathVariable("peopleUrl") String peopleUrl, ModelMap modelMap) {
        modelMap.put("peopleUrl", peopleUrl);
        return prefix + "/audit/peopleAudit";
    }

    /**
     * 跳转人员审核列表
     *
     * @param pageName 待审核/已审核页面名称
     */
    @RequiresPermissions("people:people:audit")
    @GetMapping("audit/{pageName}/{url}")
    public String audit(@PathVariable("pageName") String pageName, @PathVariable("url") String url, ModelMap modelMap) {
        String category = DictUtils.getDictLabel("sys_category", url);
        modelMap.put("url", url);
        modelMap.put("category", category);
        return prefix + "/audit/" + pageName;
    }

    /**
     * 人员查询 (返回简易对象)
     *
     * @param simplePeople 查询参数
     * @return 结果
     */
    @RequiresPermissions("people:people:list")
    @PostMapping("/simplePeopleList")
    @ResponseBody
    public TableDataInfo selectSimplePeopleList(SimplePeople simplePeople) {
        startPage();
        List<SimplePeople> list = idwPeopleMainService.selectSimplePeopleList(simplePeople);
        return getDataTable(list);
    }

    /**
     * 人员相关文件上传
     */
    @RequiresPermissions("people:people:import")
    @GetMapping("/batchUploadFile/{type}")
    public String batchUploadFile(@PathVariable("type") String type, ModelMap mmap) {
        mmap.put("type", type);
        return prefix + "/batchUploadFile";
    }

    /**
     * 获取当前分类最大的排序号
     *
     * @return 结果
     */
    @RequestMapping("/selectMaxOrderNum")
    @RequiresPermissions("office:people:list")
    @ResponseBody
    public int selectMaxOrderNum(String category) {
        return idwPeopleMainService.selectMaxOrderNum(category) + 1;
    }

    /**
     * 获取当前最大的人员编码
     *
     * @return 结果
     */
    @RequestMapping("/selectMaxPeopleCode")
    @RequiresPermissions("office:people:list")
    @ResponseBody
    public String selectMaxPeopleCode() {
        String maxPeopleCode = idwPeopleMainService.selectMaxPeopleCode();
        String newPeopleCodeNumber = "0000000" + String.valueOf(Long.parseLong(maxPeopleCode.substring(1)) + 1);
        return maxPeopleCode.charAt(0) + newPeopleCodeNumber.substring(newPeopleCodeNumber.length() - 7);
    }

    /**
     * 校验人员编码
     *
     * @return checkoutPeopleCode 人员编码是否存在
     */
    @GetMapping("/checkoutPeopleCode/{peopleCode}")
    @RequiresPermissions("people:main:list")
    @ResponseBody
    public boolean checkoutPeopleCode(@PathVariable("peopleCode") String peopleCode) {
        IdwPeopleMain idwPeopleMain = idwPeopleMainService.selectPeopleByPeopleCode(peopleCode);
        return idwPeopleMain == null;
    }

    /**
     * 根据人员编码查询
     *
     * @return 结果
     */
    @GetMapping("/selectByPeopleCode/{peopleCode}")
    @ResponseBody
    public AjaxResult selectByPeopleCode(@PathVariable("peopleCode") String peopleCode) {
        AjaxResult ajax = new AjaxResult();
        IdwPeopleMain idwPeopleMain = idwPeopleMainService.selectPeopleByPeopleCode(peopleCode);
        ajax.put("code", 200);
        ajax.put("value", idwPeopleMain);
        return ajax;
    }

    /**
     * 根据关键字查询人员信息 like查询人员名称(中英文名称&人员编码)
     */
    @GetMapping("/selectPeopleMainListByKeyword")
    @ResponseBody
    public AjaxResult selectPeopleMainListByKeyword(String keyword) {
        AjaxResult ajax = new AjaxResult();
        List<SimplePeople> simplePeopleList = idwPeopleMainService.selectPeopleByKeyword(keyword);
        ajax.put("code", 200);
        ajax.put("value", simplePeopleList);
        return ajax;
    }

    /**
     * 查询人员信息列表
     */
    @RequiresPermissions("people:people:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(IdwPeopleMain idwPeopleMain) {
        startPage();
        List<IdwPeopleMain> list = idwPeopleMainService.selectPeopleList(idwPeopleMain);
        return getDataTable(list);
    }

    /**
     * 新增保存人员信息
     */
    @RequiresPermissions("people:people:add")
    @Log(title = "人员信息", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(IdwPeopleMain idwPeopleMain) {
        return toAjax(idwPeopleMainService.insertIdwPeopleMain(idwPeopleMain));
    }

    /**
     * 修改保存人员信息
     */
    @RequiresPermissions("people:people:edit")
    @Log(title = "人员信息", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(IdwPeopleMain idwPeopleMain) {
        return toAjax(idwPeopleMainService.updatePeople(idwPeopleMain));
    }

    /**
     * 删除人员信息
     */
    @RequiresPermissions("people:people:remove")
    @Log(title = "人员信息", businessType = BusinessType.DELETE)
    @PostMapping("/removeByPeopleCode")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(idwPeopleMainService.deleteByPeopleCodes(ids));
    }

    /**
     * Excel导入跳转页面
     */
    @RequiresPermissions("people:people:remove")
    @GetMapping("/importExcel/{peopleType}")
    public String importExcel(@PathVariable("peopleType") String peopleType, ModelMap mm) {
        mm.put("peopleType", peopleType);
        return prefix + "/importExcel";
    }

    /**
     * AI解析英文简介
     */
    @RequiresPermissions("people:people:add")
    @PostMapping("/parseEnglishBio")
    @ResponseBody
    public AjaxResult parseEnglishBio(String profileEn) {
        try {
            if (StringUtils.isBlank(profileEn)) {
                return AjaxResult.error("英文简介内容不能为空");
            }

            Map<String, Object> parsedData = idwPeopleMainService.parseEnglishBiography(profileEn);
            return AjaxResult.success("AI解析成功", parsedData);
        } catch (Exception e) {
            logger.error("AI解析英文简介失败", e);
            return AjaxResult.error("AI解析失败：" + e.getMessage());
        }
    }
}
