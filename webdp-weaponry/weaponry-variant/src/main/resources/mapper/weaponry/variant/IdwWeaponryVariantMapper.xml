<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lirong.weaponry.variant.mapper.IdwWeaponryVariantMapper">
    
    <resultMap type="com.lirong.weaponry.variant.domain.IdwWeaponryVariant" id="IdwWeaponryVariantResult">
        <result property="variantId"    column="variant_id"    />
        <result property="weaponryCode"    column="weaponry_code"    />
        <result property="variantCode"    column="variant_code"    />
        <result property="variantNameCn"    column="variant_name_cn"    />
        <result property="variantNameEn"    column="variant_name_en"    />
        <result property="developmentTime"    column="development_time"    />
        <result property="frontType"    column="front_type"    />
        <result property="profile"    column="profile"    />
        <result property="source"    column="source"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectIdwWeaponryVariantVo">
        SELECT
            variant_id, weaponry_code, variant_code, variant_name_cn,
            variant_name_en, development_time, front_type, profile,
            source
        FROM
            idw_weaponry_variant
    </sql>

    <select id="selectIdwWeaponryVariantList" parameterType="com.lirong.weaponry.variant.domain.IdwWeaponryVariant" resultMap="IdwWeaponryVariantResult">
        <include refid="selectIdwWeaponryVariantVo"/>
        <where>
            is_delete = 0
            <if test="weaponryCode != null and weaponryCode != ''"> and weaponry_code = #{weaponryCode}</if>
            <if test="variantNameCn != null  and variantNameCn != ''"> and (
                variant_name_cn LIKE CONCAT( '%', #{variantNameCn}, '%' ) or variant_name_en LIKE CONCAT( '%', #{variantNameCn}, '%' )
                )</if>
        </where>
    </select>
    
    <select id="selectIdwWeaponryVariantById" parameterType="Long" resultMap="IdwWeaponryVariantResult">
        <include refid="selectIdwWeaponryVariantVo"/>
        where is_delete = 0 and variant_id = #{variantId}
    </select>

    <!--根据武器装备编码查询-->
    <select id="selectByWeaponryCode" resultMap="IdwWeaponryVariantResult">
        <include refid="selectIdwWeaponryVariantVo"/>
        where is_delete = 0 and weaponry_code = #{weaponryCode}
    </select>

    <!--根据武器装备编码与变体名称查询-->
    <select id="selectByWeaponryCodeAndName" resultMap="IdwWeaponryVariantResult">
        <include refid="selectIdwWeaponryVariantVo"/>
        where is_delete = 0
            and weaponry_code = #{weaponryCode}
        <if test="variantNameCn != null and variantNameCn != ''">and variant_name_cn = #{variantNameCn}</if>
        <if test="variantNameEn != null and variantNameEn != ''">and variant_name_en = #{variantNameEn}</if>
    </select>

    <!--根据关键字和武器装备编码查询-->
    <select id="selectByWeaponryCodeAndKeyword" resultMap="IdwWeaponryVariantResult">
        SELECT
            variant_name_cn,
            variant_name_en
        FROM
            idw_weaponry_variant
        WHERE
            is_delete = 0
        <if test="weaponryCode != null and weaponryCode != ''">
            AND weaponry_code = #{weaponryCode}
        </if>
        <if test="keyword != null and keyword != ''">
            AND ( variant_name_cn LIKE CONCAT( '%', #{keyword}, '%' ) OR variant_name_en LIKE CONCAT( '%', #{keyword}, '%' ) )
        </if>
    </select>

    <!--根据武器装备编码查询-->
    <select id="selectByWeaponryCodes" resultMap="IdwWeaponryVariantResult">
        SELECT
            variant_id, weaponry_code, variant_code, variant_name_cn,
            variant_name_en, development_time, front_type, profile,
            source
        FROM
            idw_weaponry_variant
        WHERE
            is_delete = 0
        <if test="weaponryCodes != null and weaponryCodes.length > 0">
            AND weaponry_code IN
            <foreach item="weaponryCode" collection="weaponryCodes" open="(" separator="," close=")">
                #{weaponryCode}
            </foreach>
        </if>
    </select>

    <insert id="insertIdwWeaponryVariant" parameterType="com.lirong.weaponry.variant.domain.IdwWeaponryVariant" useGeneratedKeys="true" keyProperty="variantId">
        insert into idw_weaponry_variant
        <trim prefix="(" suffix=")" suffixOverrides=",">
            is_delete,
            <if test="weaponryCode != null and weaponryCode != ''">weaponry_code,</if>
            <if test="variantCode != null and variantCode != ''">variant_code,</if>
            <if test="variantNameCn != null and variantNameCn != ''">variant_name_cn,</if>
            <if test="variantNameEn != null and variantNameEn != ''">variant_name_en,</if>
            <if test="developmentTime != null and developmentTime != ''">development_time,</if>
            <if test="frontType != null and frontType != ''">front_type,</if>
            <if test="profile != null and profile != ''">profile,</if>
            <if test="source != null and source != ''">source,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            0,
            <if test="weaponryCode != null and weaponryCode != ''">#{weaponryCode},</if>
            <if test="variantCode != null and variantCode != ''">#{variantCode},</if>
            <if test="variantNameCn != null and variantNameCn != ''">#{variantNameCn},</if>
            <if test="variantNameEn != null and variantNameEn != ''">#{variantNameEn},</if>
            <if test="developmentTime != null and developmentTime != ''">#{developmentTime},</if>
            <if test="frontType != null and frontType != ''">#{frontType},</if>
            <if test="profile != null and profile != ''">#{profile},</if>
            <if test="source != null and source != ''">#{source},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateIdwWeaponryVariant" parameterType="com.lirong.weaponry.variant.domain.IdwWeaponryVariant">
        update idw_weaponry_variant
        <trim prefix="SET" suffixOverrides=",">
            <if test="weaponryCode != null">weaponry_code = #{weaponryCode},</if>
            <if test="variantCode != null">variant_code = #{variantCode},</if>
            <if test="variantNameCn != null">variant_name_cn = #{variantNameCn},</if>
            <if test="variantNameEn != null">variant_name_en = #{variantNameEn},</if>
            <if test="developmentTime != null">development_time = #{developmentTime},</if>
            <if test="frontType != null">front_type = #{frontType},</if>
            <if test="profile != null">profile = #{profile},</if>
            <if test="source != null">source = #{source},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where variant_id = #{variantId}
    </update>

    <update id="deleteIdwWeaponryVariantByIds">
        update idw_weaponry_variant
        SET update_by = #{loginName},
        update_time = sysdate(),
        IS_DELETE = 1
        WHERE variant_id in
        <foreach item="variantId" collection="variantIds" open="(" separator="," close=")">
            #{variantId}
        </foreach>
    </update>

    <!--根据武器装备编码删除-->
    <update id="deleteByWeaponryCode">
        update idw_weaponry_variant
        SET update_by = #{loginName},
        update_time = sysdate(),
        IS_DELETE = 1
        WHERE weaponry_code = #{weaponryCode}
    </update>

    <!--根据武器装备编码删除-->
    <update id="deleteByWeaponryCodes">
        update idw_weaponry_variant
        SET update_by = #{loginName},
        update_time = sysdate(),
        weaponry_code = CONCAT( #{deleteTime} , '-' , #{loginName} , '-' , weaponry_code ),
        IS_DELETE = 1
        WHERE weaponry_code in
        <foreach item="weaponryCode" collection="weaponryCodes" open="(" separator="," close=")">
            #{weaponryCode}
        </foreach>
    </update>

</mapper>