package com.lirong.weaponry.theme.mapper;

import com.lirong.weaponry.basic.domain.IdwWeaponryBasic;
import com.lirong.weaponry.theme.domain.IdwWeaponryAffiliationTheme;
import com.lirong.weaponry.theme.domain.IdwWeaponryTheme;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 武器装备所属主题Mapper接口
 *
 * <AUTHOR>
 * @date 2021-09-16
 */
public interface IdwWeaponryAffiliationThemeMapper {

    /**
     * 查询武器装备主题库关联装备
     *
     * @param idwWeaponryAffiliationTheme 查询参数
     * @return 结果
     */
    public List<IdwWeaponryBasic> selectWeaponryAffiliationThemeList(IdwWeaponryAffiliationTheme idwWeaponryAffiliationTheme);

    /**
     * 根据主题ID查询关联装备编码
     *
     * @param themeId 主题ID
     * @return 结果
     */
    public List<String> selctWeaponryCodeByThemeId(Long themeId);

    /**
     * 根据主题ID&装备编码删除关联
     *
     * @param themeId       主题ID
     * @param weaponryCodes 装备编码
     * @param userName      当前登录用户
     * @return 结果
     */
    public int deleteByThemeIdAndWeaponryCode(@Param("themeId") Long themeId, @Param("weaponryCodes") String[] weaponryCodes, @Param("userName") String userName);

    /**
     * 新增关系
     *
     * @param weaponryAffiliationThemeList 关系对象
     * @return 结果
     */
    public int insert(List<IdwWeaponryAffiliationTheme> weaponryAffiliationThemeList);

    /**
     * 根据ID删除关系
     *
     * @param affiliationIds 关系表ID
     * @return 结果
     */
    public int deleteById(@Param("affiliationIds") Long[] affiliationIds, @Param("userName") String userName);

    /**
     * 根据主题ID和武器装备编码查询数据是否存在
     *
     * @param themeId      主题ID
     * @param weaponryCode 武器装备编码
     * @param databaseType 数据库类型
     * @return 结果
     */
    public boolean selectByThemeIdAndWeaponry(@Param("themeId") Long themeId, @Param("weaponryCode") String weaponryCode, @Param("databaseType") String databaseType);

    /**
     * 根据父主题ID与主题名称查询
     *
     * @param parentId 父主题ID
     * @param name     主题名称
     * @return 结果
     */
    public IdwWeaponryTheme selectByParentIdAndName(@Param("parentId") Long parentId, @Param("name") String name);
}
