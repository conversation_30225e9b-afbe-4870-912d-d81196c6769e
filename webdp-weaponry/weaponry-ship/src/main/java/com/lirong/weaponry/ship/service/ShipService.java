package com.lirong.weaponry.ship.service;

import com.lirong.weaponry.ship.domain.Ship;

import java.util.List;
import java.util.Map;

/**
 * 舰船Service接口
 *
 * <AUTHOR>
 * @date 2023-02-02
 */
public interface ShipService {

    /**
     * 查询舰船
     *
     * @param id 舰船ID
     * @return 舰船
     */
    public Ship selectShipById(Long id);

    /**
     * 新增舰船
     *
     * @param ship 舰船
     * @return 结果
     */
    public int insertShip(Ship ship);

    /**
     * 修改舰船
     *
     * @param ship 舰船
     * @return 结果
     */
    public int updateShip(Ship ship);

    /**
     * 根据武器装备编码查询舰船
     *
     * @param weaponryCode 武器装备编码
     * @return 结果
     */
    public Ship selectShipByWeaponryCode(String weaponryCode);

    /**
     * 根据关键字查询舰船 like查询舰船名称与舷号并排除自己
     *
     * @param weaponryCode   当前装备编码（需排除）
     * @param nationalOrigin 国家
     * @param keyword        关键词
     * @return 结果
     */
    public List<Ship> selectShipByKeyword(String weaponryCode, String nationalOrigin, String keyword);

    /**
     * 导入校验数据格式
     *
     * @param shipList         舰船数据列表
     * @param filePathIndexMap key 文件在压缩包中的相对路径 value 文件对应在filePathList中的索引
     * @param filePathList     上传后的文件路径
     * @param baseDir          临时文件夹目录
     * @param updateSupport    是否更新
     */
    public List<String> verifyImportShip(List<Ship> shipList, Map<String, Integer> filePathIndexMap, List<String> filePathList, String baseDir, boolean updateSupport);

    /**
     * 导入舰船
     *
     * @param updateSupport 是否支持更新, 如果已存在, 则进行更新
     * @param operName      操作用户
     * @return 结果
     */
    public String importShip(boolean updateSupport, String operName);

    /**
     * 根据武器装备编码查询
     *
     * @param weaponryCodes 武器装备编码
     * @return 结果
     */
    public List<Ship> selectShipByWeaponryCodes(String[] weaponryCodes);

    /**
     * 查询舰船当前最大排序号
     *
     * @return 当前装备最大排序号
     */
    public Integer selectShipMaxOrderNum();
}
