<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增装备分类结构')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-structure-add">
            <input type="hidden" name="classificationCode" th:value="${classificationCode}">
            <input type="hidden" name="parentCode" th:value="${parentCode}">
            <div class="form-group">
                <label class="col-sm-3 control-label">上级结构：</label>
                <div class="col-sm-8">
                    <input name="classificationName" th:value="${classificationName}" class="form-control" type="text" readonly>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">结构中文名称：</label>
                <div class="col-sm-8">
                    <input name="nameCn" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">结构英文名称：</label>
                <div class="col-sm-8">
                    <input name="nameEn" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">简介：</label>
                <div class="col-sm-8">
                    <textarea name="introduction" class="form-control" rows="6"></textarea>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">数据来源：</label>
                <div class="col-sm-8">
                    <input name="source" class="form-control" type="text" required>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        let prefix = ctx + "weaponryClassification/structure"
        $("#form-structure-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/add", $('#form-structure-add').serialize(), refreshStructureTree);
            }
        }

        //刷新武器装备分类结构结构树
        function refreshStructureTree() {
            window.parent.queryClassificationStructureTree();
        }
    </script>
</body>
</html>