<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增事件')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-event-add">
            <div class="form-group text-center">
                <input type="hidden" name="icon" id="icon">
                <p class="user-info-head" onclick="importImg()">
                    <img class="img-lg" id="iconUrl" th:src="@{/img/default.png}" th:onerror="'this.src=\'' + @{'/img/default.png'} + '\''">
                </p>
                <p><input type="file" id="iconInput" style="display: none;"></p>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">事件名称：</label>
                <div class="col-sm-8">
                    <input name="name" class="form-control" type="text" required>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        let prefix = ctx + "event/event"
        $("#form-event-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/add", $('#form-event-add').serialize());
            }
        }

        //事件图标上传
        function importImg() {
                $('#iconInput').trigger('click');
        }
        $("#iconInput").change(function () {
            var data = new FormData();
            data.append("file", $("#iconInput")[0].files[0]);
            $.ajax({
                type: "POST",
                url: ctx + "common/upload/img",
                data: data,
                cache: false,
                contentType: false,
                processData: false,
                dataType: 'json',
                success: function(result) {
                    if (result.code == web_status.SUCCESS) {
                        $("#iconUrl").attr("src",result.url)
                        $("#icon").val(result.url)
                    }
                },
                error: function(error) {
                    alert("事件图标上传失败。");
                }
            });
        });
    </script>
</body>
</html>