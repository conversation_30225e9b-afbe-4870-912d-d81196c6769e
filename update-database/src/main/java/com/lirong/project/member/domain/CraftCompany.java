package com.lirong.project.member.domain;

import com.lirong.common.annotation.Excel;
import com.lirong.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 采集机构对象 craft_company
 *
 * <AUTHOR>
 * @date 2022-12-22
 */
public class CraftCompany extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Excel(name = "company_id")
    private Long id;

    /**
     * 类型
     */
    @Excel(name = "类型")
    private String type;

    /**
     * Logo
     */
    @Excel(name = "logo")
    private String logo;

    /**
     * 名称
     */
    @Excel(name = "display_name")
    private String displayName;

    /**
     * 全称
     */
    @Excel(name = "full_name")
    private String fullName;

    /**
     * 机构唯一标识
     */
    @Excel(name = "机构唯一标识")
    private String uei;

    /**
     * Slug
     */
    @Excel(name = "Slug")
    private String slug;

    /**
     * 国家
     */
    @Excel(name = "country")
    private String country;

    /**
     * 成立年份
     */
    @Excel(name = "founded_year")
    private Integer foundedYear;

    /**
     * 员工数量
     */
    @Excel(name = "employee_count")
    private Integer employeeCount;

    /**
     * 总部地址
     */
    @Excel(name = "hq_location")
    private String hqLocation;

    /**
     * 纬度
     */
    @Excel(name = "latitude")
    private String latitude;

    /**
     * 经度
     */
    @Excel(name = "longitude")
    private String longitude;

    /**
     * 领域
     */
    @Excel(name = "sectors")
    private String sectors;

    /**
     * 官方网站
     */
    @Excel(name = "website")
    private String website;

    /**
     * 简介
     */
    @Excel(name = "short_description")
    private String shortDescription;

    /**
     * 营业状态
     */
    @Excel(name = "status")
    private String status;

    /**
     * 数据来源
     */
    @Excel(name = "source")
    private String source;

    /**
     * LinkedIn地址
     */
    @Excel(name = "linkedin")
    private String linkedin;

    /**
     * Instagram地址
     */
    @Excel(name = "instagram")
    private String instagram;

    /**
     * Facebook地址
     */
    @Excel(name = "facebook")
    private String facebook;

    /**
     * Youtube地址
     */
    @Excel(name = "youtube")
    private String youtube;

    /**
     * Twitter地址
     */
    @Excel(name = "twitter")
    private String twitter;

    /**
     * 概览
     */
    @Excel(name = "overview")
    private String overview;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public String getLogo() {
        return logo;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getFullName() {
        return fullName;
    }

    public void setUei(String uei) {
        this.uei = uei;
    }

    public String getUei() {
        return uei;
    }

    public void setSlug(String slug) {
        this.slug = slug;
    }

    public String getSlug() {
        return slug;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getCountry() {
        return country;
    }

    public void setFoundedYear(Integer foundedYear) {
        this.foundedYear = foundedYear;
    }

    public Integer getFoundedYear() {
        return foundedYear;
    }

    public void setEmployeeCount(Integer employeeCount) {
        this.employeeCount = employeeCount;
    }

    public Integer getEmployeeCount() {
        return employeeCount;
    }

    public void setHqLocation(String hqLocation) {
        this.hqLocation = hqLocation;
    }

    public String getHqLocation() {
        return hqLocation;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setSectors(String sectors) {
        this.sectors = sectors;
    }

    public String getSectors() {
        return sectors;
    }

    public void setWebsite(String website) {
        this.website = website;
    }

    public String getWebsite() {
        return website;
    }

    public void setShortDescription(String shortDescription) {
        this.shortDescription = shortDescription;
    }

    public String getShortDescription() {
        return shortDescription;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getSource() {
        return source;
    }

    public void setLinkedin(String linkedin) {
        this.linkedin = linkedin;
    }

    public String getLinkedin() {
        return linkedin;
    }

    public void setInstagram(String instagram) {
        this.instagram = instagram;
    }

    public String getInstagram() {
        return instagram;
    }

    public void setFacebook(String facebook) {
        this.facebook = facebook;
    }

    public String getFacebook() {
        return facebook;
    }

    public void setYoutube(String youtube) {
        this.youtube = youtube;
    }

    public String getYoutube() {
        return youtube;
    }

    public void setTwitter(String twitter) {
        this.twitter = twitter;
    }

    public String getTwitter() {
        return twitter;
    }

    public void setOverview(String overview) {
        this.overview = overview;
    }

    public String getOverview() {
        return overview;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("type", getType())
                .append("logo", getLogo())
                .append("displayName", getDisplayName())
                .append("fullName", getFullName())
                .append("uei", getUei())
                .append("slug", getSlug())
                .append("country", getCountry())
                .append("foundedYear", getFoundedYear())
                .append("employeeCount", getEmployeeCount())
                .append("hqLocation", getHqLocation())
                .append("latitude", getLatitude())
                .append("longitude", getLongitude())
                .append("sectors", getSectors())
                .append("website", getWebsite())
                .append("shortDescription", getShortDescription())
                .append("status", getStatus())
                .append("source", getSource())
                .append("linkedin", getLinkedin())
                .append("instagram", getInstagram())
                .append("facebook", getFacebook())
                .append("youtube", getYoutube())
                .append("twitter", getTwitter())
                .append("overview", getOverview())
                .toString();
    }
}