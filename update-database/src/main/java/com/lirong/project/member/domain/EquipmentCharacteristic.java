package com.lirong.project.member.domain;

import com.lirong.common.annotation.Excel;
import com.lirong.common.core.domain.BaseEntity;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

public class EquipmentCharacteristic extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 装备ID
     */
    @Excel(name = "装备ID")
    private Long equipmentId;

    /**
     * 指标类型
     */
    @Excel(name = "指标类型")
    private String type;

    /**
     * 指标名称
     */
    @Excel(name = "指标名称")
    private String indexName;

    /**
     * 指标值
     */
    @Excel(name = "指标值")
    private String indexValue;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setEquipmentId(Long equipmentId) {
        this.equipmentId = equipmentId;
    }

    public Long getEquipmentId() {
        return equipmentId;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public void setIndexName(String indexName) {
        this.indexName = indexName;
    }

    public String getIndexName() {
        return indexName;
    }

    public void setIndexValue(String indexValue) {
        this.indexValue = indexValue;
    }

    public String getIndexValue() {
        return indexValue;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("equipmentId", getEquipmentId())
                .append("type", getType())
                .append("indexName", getIndexName())
                .append("indexValue", getIndexValue())
                .toString();
    }
}