package com.lirong.nlp.pipeline;

import com.lirong.common.utils.StringUtils;
import edu.stanford.nlp.ling.CoreAnnotation;
import edu.stanford.nlp.pipeline.Annotation;
import edu.stanford.nlp.pipeline.CoreDocument;
import edu.stanford.nlp.pipeline.StanfordCoreNLP;
import edu.stanford.nlp.util.CoreMap;
import edu.stanford.nlp.util.ErasureUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.List;
import java.util.Properties;

/**
 * Author：houjianjun
 * Date：2022/5/5
 * Description：<说明>
 */
//@Component
public class NERTask {

    StanfordCoreNLP enPipeline;
    StanfordCoreNLP zhPipeline;

    // key for matched expressions
    public static class MyMatchedExpressionAnnotation implements CoreAnnotation<List<CoreMap>> {
        @Override
        public Class<List<CoreMap>> getType() {
            return ErasureUtils.<Class<List<CoreMap>>> uncheckedCast(String.class);
        }
    }

    @PostConstruct
    void init() throws IOException {
        Properties en_props = new Properties();
        en_props.load(this.getClass().getResourceAsStream("/StanfordCoreNLP.properties"));
        // 英文
        // props.put("annotators", "tokenize, ssplit, pos, lemma, ner, regexner");
        // props.setProperty("ner.additional.regexner.mapping", "weaponry.rules");
        // props.setProperty("ner.additional.regexner.ignorecase", "true");
        //
        // props.setProperty("tokensregex.rules", "contract.rules");
        // props.setProperty("tokensregex.matchedExpressionsAnnotationKey",
        //         "com.lirong.nlp.pipeline.NERTask$MyMatchedExpressionAnnotation");
        // props.setProperty("tokenize.options", "splitForwardSlash=false,splitHyphenated=false,invertible");

//        props.setProperty("ner.combinationMode", "HIGH_RECALL");
        enPipeline = new StanfordCoreNLP(en_props);

        Properties zh_props = new Properties();
        // 中文
        zh_props.load(this.getClass().getResourceAsStream("/StanfordCoreNLP-chinese.properties"));


//        props.setProperty("ner.additional.regexner.mapping", "/Users/<USER>/Development/workspaces/workspace-github/CoreNLP/test/src/edu/stanford/nlp/simple/weaponry.rules");
//        props.setProperty("ner.additional.regexner.ignorecase", "true");

        zhPipeline = new StanfordCoreNLP(zh_props);
    }

    public Annotation parse(String content, String language) {
        // make an example document
        Annotation annotation = new Annotation(content);
        if (StringUtils.isEmpty(language)) {
            enPipeline.annotate(annotation);
        } else {
            if ("zh".equals(language)) {
                zhPipeline.annotate(annotation);
            } else {
                enPipeline.annotate(annotation);
            }
        }
        return annotation;
    }

}
