<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('解析文本')" />
    <!-- NLP -->
    <th:block th:include="include :: nlp-parse-css" />
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="form-contract-add">
        <div class="row">
            <div class="col-sm-12">
                <div class="form-group">
                    <h4 class="red">解析内容:</h4>
                    <textarea name="content" id="text" class="form-control" rows="8" style="font-size: 14px;"></textarea>
                </div>
                <div class="form-group">
                    <div class="col-sm-9"></div>
                    <div class="col-sm-2">
                        选择语言：
                        <select id="language" name="language">
                            <option value="en">English</option>
                            <option value="zh">中文</option>
                        </select>
                    </div>
                    <div class="col-sm-1" style="text-align: right;">
                        <button type="button" class="btn btn-sm btn-info" id="submit"><i class="fa"></i>解 析</button>
                    </div>
                </div>
            </div>
            <div class="col-sm-12">
                <!-- Loading gif -->
                <div id="loading" class="row" style="text-align: center; display:none;">
                    <img th:src="@{/img/loading.gif}" height="100px" style="margin-left: 100px"/>
                </div>
                <!-- Annotation population area -->
                <div id="annotations" class="row" style="display:none">
                </div>

                <div id="entity-map" class="row">
                </div>

                <div id="relation-container" class="row">
                </div>
            </div>
        </div>
    </form>
</div>
<div class="row">
    <div class="col-sm-offset-5 col-sm-10">

    </div>
</div>
<th:block th:include="include :: footer" />
<th:block th:include="include :: nlp-parse-js" />
<script type="text/javascript">

</script>
</body>
</html>