package com.lirong.multimedia.service.impl;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.util.*;

import com.lirong.common.config.WebdpConfig;
import com.lirong.common.constant.Constants;
import com.lirong.common.utils.CacheUtils;
import com.lirong.common.utils.DateUtils;
import com.lirong.common.utils.ShiroUtils;
import com.lirong.common.utils.StringUtils;
import com.lirong.common.utils.file.FileUploadUtils;
import com.lirong.common.utils.file.FileUtils;
import com.lirong.common.utils.file.ImageUtils;
import com.lirong.personnel.common.domain.IdwPeopleMain;
import com.lirong.personnel.common.mapper.IdwPeopleMainMapper;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lirong.multimedia.mapper.IdwMultimediaMapper;
import com.lirong.multimedia.domain.IdwMultimedia;
import com.lirong.multimedia.service.IdwMultimediaService;
import com.lirong.common.core.text.Convert;

/**
 * 多媒体Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-03-22
 */
@Service
public class IdwMultimediaServiceImpl implements IdwMultimediaService {
    // 声明图片后缀名数组
    String imgeArray[][] = {
            {"bmp", "0"}, {"dib", "1"}, {"gif", "2"},
            {"jfif", "3"}, {"jpe", "4"}, {"jpeg", "5"},
            {"jpg", "6"}, {"png", "7"}, {"tif", "8"},
            {"tiff", "9"}, {"ico", "10"}
    };

    @Autowired
    private IdwMultimediaMapper idwMultimediaMapper;
    @Autowired//人员
    private IdwPeopleMainMapper idwPeopleMainMapper;

    /**
     * 查询多媒体
     *
     * @param mediaId 多媒体ID
     * @return 多媒体
     */
    @Override
    public IdwMultimedia selectIdwMultimediaById(Long mediaId) {
        return idwMultimediaMapper.selectIdwMultimediaById(mediaId);
    }

    /**
     * 根据媒体类型与关联ID查询
     *
     * @param businessType   媒体类型
     * @param associationIds 关联ID
     * @return 结果
     */
    @Override
    public List<IdwMultimedia> selectByBusinessTypeAndAssociationIds(String businessType, Object[] associationIds) {
        return idwMultimediaMapper.selectByBusinessTypeAndAssociationIds(businessType, associationIds);
    }

    /**
     * 查询多媒体列表
     *
     * @param idwMultimedia 多媒体
     * @return 多媒体
     */
    @Override
    public List<IdwMultimedia> selectIdwMultimediaList(IdwMultimedia idwMultimedia) {
        return idwMultimediaMapper.selectIdwMultimediaList(idwMultimedia);
    }

    /**
     * 判断文件是否为图片
     *
     * @param pInput    文件名
     * @param pImgeFlag 判断具体文件类型
     * @return 检查后的结果
     * @throws Exception
     */
    public static boolean isPicture(String pInput, String pImgeFlag) {
        // 文件名称为空的场合
        if (StringUtils.isBlank(pInput)) {
            // 返回不和合法
            return false;
        }
        // 获得文件后缀名
        String tmpName = pInput.substring(pInput.lastIndexOf(".") + 1,
                pInput.length());
        // 声明图片后缀名数组
        String imgeArray[][] = {
                {"bmp", "0"}, {"dib", "1"}, {"gif", "2"},
                {"jfif", "3"}, {"jpe", "4"}, {"jpeg", "5"},
                {"jpg", "6"}, {"png", "7"}, {"tif", "8"},
                {"tiff", "9"}, {"ico", "10"}
        };
        // 遍历名称数组
        for (int i = 0; i < imgeArray.length; i++) {
            // 判断单个类型文件的场合
            if (!StringUtils.isBlank(pImgeFlag)
                    && imgeArray[i][0].equals(tmpName.toLowerCase())
                    && imgeArray[i][1].equals(pImgeFlag)) {
                return true;
            }
            // 判断符合全部类型的场合
            if (StringUtils.isBlank(pImgeFlag)
                    && imgeArray[i][0].equals(tmpName.toLowerCase())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 生成缩略图
     *
     * @return 多媒体集合
     */
    @Override
    public void generateThumbnail() {
        List<IdwMultimedia> multimediaList = idwMultimediaMapper.selectIdwMultimediaList(null);
        try {
            for (IdwMultimedia multimedia : multimediaList) {
                if (StringUtils.isBlank(multimedia.getThumbnail())
                        && isPicture(multimedia.getStoragePath(), null)) {
                    // 生成缩略图
                    String thumbnail = ImageUtils.generateThumbnail(multimedia.getStoragePath());
                    // 更新缩略图属性
                    multimedia.setThumbnail(thumbnail);
                    idwMultimediaMapper.updateIdwMultimedia(multimedia);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 新增多媒体
     *
     * @param idwMultimedia 多媒体
     * @return 结果
     */
    @Override
    public int insertIdwMultimedia(IdwMultimedia idwMultimedia) {
        String storagePath = idwMultimedia.getStoragePath();
        if (StringUtils.isBlank(idwMultimedia.getThumbnail())) {
            if (storagePath.contains(Constants.THUMBNAIL_PREFIX)) {
                idwMultimedia.setThumbnail(storagePath);
                idwMultimedia.setStoragePath(storagePath.replace(Constants.THUMBNAIL_PREFIX, ""));
            }
        }
        //如果文件类型/后缀为空 截取并赋值
        if (StringUtils.isBlank(idwMultimedia.getMediaType()) && StringUtils.isNotBlank(storagePath)) {
            idwMultimedia.setMediaType(idwMultimedia.getStoragePath().substring(idwMultimedia.getStoragePath().lastIndexOf(".")));
        }
        if (StringUtils.isBlank(idwMultimedia.getMd5()) && StringUtils.isNotBlank(storagePath)) {
            idwMultimedia.setMd5(FileUtils.getMd5(storagePath));
        }
        idwMultimedia.setCreateBy(ShiroUtils.getUserName());
        idwMultimedia.setCreateTime(DateUtils.getNowDate());
        return idwMultimediaMapper.insertIdwMultimedia(idwMultimedia);
    }

    /**
     * 修改多媒体
     *
     * @param idwMultimedia 多媒体
     * @return 结果
     */
    @Override
    public int updateIdwMultimedia(IdwMultimedia idwMultimedia) {
        //如果文件类型/后缀为空 截取并赋值
        if (StringUtils.isBlank(idwMultimedia.getMediaType()) && StringUtils.isNotBlank(idwMultimedia.getStoragePath())) {
            idwMultimedia.setMediaType(idwMultimedia.getStoragePath().substring(idwMultimedia.getStoragePath().lastIndexOf(".")));
        }
        idwMultimedia.setUpdateBy(ShiroUtils.getUserName());
        idwMultimedia.setUpdateTime(DateUtils.getNowDate());
        if (StringUtils.isBlank(idwMultimedia.getMd5())) {
            String filePath = idwMultimedia.getStoragePath().replace(Constants.RESOURCE_PREFIX, WebdpConfig.getPath());
            File file = new File(filePath);
            if (file.exists()) {
                try {
                    InputStream inputStream = new FileInputStream(file);
                    String md5 = DigestUtils.md5Hex(String.valueOf(inputStream));
                    idwMultimedia.setMd5(md5);
                } catch (FileNotFoundException e) {
                    e.printStackTrace();
                }
            }
        }
        return idwMultimediaMapper.updateIdwMultimedia(idwMultimedia);
    }

    /**
     * 删除多媒体对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteIdwMultimediaByIds(String ids) {
        String loginName = ShiroUtils.getUserName();
        return idwMultimediaMapper.deleteIdwMultimediaByIds(Convert.toStrArray(ids), loginName);
    }

    /**
     * 校验Excel导入数据
     *
     * @param multimediaList   多媒体集合
     * @param filePathIndexMap key 文件在压缩包中的相对路径 value 文件对应在filePathList中的索引
     * @param filePathList     上传后的文件路径
     * @param baseDir          临时文件夹目录
     * @param updateSupport    是否更新
     * @param businessType     关联类型 人员 机构 武器装备
     * @param msgTitle         提示标题
     * @return 结果
     */
    @Override
    public List<String> verifyImportMultimedia(List<IdwMultimedia> multimediaList, Map<String, Integer> filePathIndexMap, List<String> filePathList, String baseDir, boolean updateSupport, String businessType, String msgTitle) {
        if (StringUtils.isNull(multimediaList) || multimediaList.size() < 1) {
            return null;
        }
        //处理完成图片视频数据列表
        List<IdwMultimedia> treatingAfterMultimediaList = new ArrayList<>();
        int row = 1;
        boolean isFailure = false;
        List<String> msgList = new ArrayList<>();
        String userName = ShiroUtils.getUserName();
        //人员编码
        List<String> peopleCodeList = (List<String>) CacheUtils.get("peopleImportTreatingAfterPeopleCodeList-" + userName);
        List<String> temporaryFilePathList = (List<String>) CacheUtils.get("peopleImportTreatingTemporaryFilePathList-" + userName);//需要拷贝的文件路径
        if (StringUtils.isNull(temporaryFilePathList)) {
            temporaryFilePathList = new ArrayList<>();
        }
        List<String> newFilePathList = (List<String>) CacheUtils.get("peopleImportTreatingNewFilePathList-" + userName);//拷贝文件目标路径
        if (StringUtils.isNull(newFilePathList)) {
            newFilePathList = new ArrayList<>();
        }
        for (IdwMultimedia multimedia : multimediaList) {
            row++;
            if (multimedia != null) {
                multimedia.setBusinessType(businessType);
                multimedia.setTag(multimedia.getTag().replaceAll("、", ",").replaceAll(" ", ""));
                multimedia.setMediaType("");
                if (StringUtils.isBlank(multimedia.getAssociationId())) {
                    isFailure = true;
                    msgList.add(msgTitle + ",第" + row + "行," + " 人员编码为空");
                } else {
                    String peopleCode = multimedia.getAssociationId();
                    IdwPeopleMain people = idwPeopleMainMapper.selectPeopleByPeopleCode(peopleCode);
                    if (StringUtils.isNull(people) && (StringUtils.isNull(peopleCodeList) || !peopleCodeList.contains(peopleCode))) {
                        isFailure = true;
                        msgList.add(msgTitle + ",第" + row + "行," + " 人员编码（" + peopleCode + "）不存在");
                    }
                }
                if (StringUtils.isBlank(multimedia.getTitle())) {
                    isFailure = true;
                    msgList.add(msgTitle + ",第" + row + "行," + " 标题为空");
                }
                if (StringUtils.isBlank(multimedia.getSource())) {
                    isFailure = true;
                    msgList.add(msgTitle + ",第" + row + "行," + " 数据来源为空");
                }
                // 判断是否存在
                IdwMultimedia oldMultimedia = idwMultimediaMapper.selectByBusinessTypeAndAssociationIdAndTitle(multimedia.getBusinessType(), multimedia.getAssociationId(), multimedia.getTitle());
                if (StringUtils.isNotNull(oldMultimedia)) {
                    multimedia.setMediaId(oldMultimedia.getMediaId());
                }
                //处理图片视频
                String dimensionalityFilePath = "";
                if (businessType.equals("weaponry")) {
                    dimensionalityFilePath = WebdpConfig.getWeaponryPath();
                }
                if (businessType.equals("organization")) {
                    dimensionalityFilePath = WebdpConfig.getOrganizationPath();
                }
                if (businessType.equals("personnel")) {
                    dimensionalityFilePath = WebdpConfig.getPersonnelPath();
                }
                String oldStoragePath = multimedia.getStoragePath();
                if (StringUtils.isBlank(oldStoragePath)) {
                    isFailure = true;
                    msgList.add(msgTitle + ",第" + row + "行," + " 图片视频为空");
                } else {
                    //替换文件名称中的特殊字符
                    oldStoragePath = FileUploadUtils.replaceFileNameSpecialCharacter(oldStoragePath).toLowerCase();
                    if (StringUtils.isNotNull(filePathIndexMap) && filePathIndexMap.size() > 0) {
                        Integer index = filePathIndexMap.get(oldStoragePath);
                        //不存在 匹配文件名称
                        if (StringUtils.isNull(index)) {
                            String excelFileName = StringUtils.getFileName(oldStoragePath);
                            //使用文件名称匹配
                            for (String key : filePathIndexMap.keySet()) {
                                String fileName = StringUtils.getFileName(key);
                                if (excelFileName.equals(fileName)) {
                                    index = filePathIndexMap.get(key);
                                    break;
                                }
                            }
                        }
                        if (StringUtils.isNotNull(index)) {
                            //当数据存在但是支持更新或数据不存在时处理文件
                            if ((StringUtils.isNotNull(oldMultimedia) && updateSupport) || StringUtils.isNull(oldMultimedia)) {
                                //获取文件名称
                                String temporaryFilePath = filePathList.get(index);
                                String[] filePathArr = temporaryFilePath.split("/");
                                //根据文件名称与纬度路径生成新的文件路径
                                String newFilePath = FileUploadUtils.generateFilePath(dimensionalityFilePath, filePathArr[filePathArr.length - 1]);
                                //保存文件路径
                                temporaryFilePathList.add(temporaryFilePath);
                                //保存新的文件路径
                                newFilePathList.add(newFilePath);
                                //赋值新文件路径
                                multimedia.setStoragePath(newFilePath);
                                //计算文件md5
                                String md5 = FileUtils.getMd5(temporaryFilePath);
                                multimedia.setMd5(md5);
                            }
                        } else {
                            isFailure = true;
                            msgList.add(msgTitle + ",第" + row + "行," + " 图片视频（" + oldStoragePath + "）不存在");
                        }
                    } else {
                        isFailure = true;
                        msgList.add(msgTitle + ",第" + row + "行," + " 图片视频不为空且压缩包文件为空");
                    }
                }
                //处理缩略图
                String oldThumbnailPath = multimedia.getThumbnail();
                //替换缩略图名称中的特殊字符
                oldThumbnailPath = FileUploadUtils.replaceFileNameSpecialCharacter(oldThumbnailPath).toLowerCase();
                if (StringUtils.isNotBlank(oldThumbnailPath)) {
                    if (StringUtils.isNotNull(filePathIndexMap) && filePathIndexMap.size() > 0) {
                        Integer index = filePathIndexMap.get(oldThumbnailPath);
                        //不存在 匹配文件名称
                        if (StringUtils.isNull(index)) {
                            String excelFileName = StringUtils.getFileName(oldThumbnailPath);
                            //使用文件名称匹配
                            for (String key : filePathIndexMap.keySet()) {
                                String fileName = StringUtils.getFileName(key);
                                if (excelFileName.equals(fileName)) {
                                    index = filePathIndexMap.get(key);
                                    break;
                                }
                            }
                        }
                        if (StringUtils.isNotNull(index)) {
                            //当数据存在但是支持更新或数据不存在时处理缩略图
                            if ((StringUtils.isNotNull(oldMultimedia) && updateSupport) || StringUtils.isNull(oldMultimedia)) {
                                //获取缩略图名称
                                String temporaryFilePath = filePathList.get(index);
                                String[] filePathArr = temporaryFilePath.split("/");
                                //根据缩略图名称与纬度路径生成新的缩略图路径
                                String newFilePath = FileUploadUtils.generateFilePath(dimensionalityFilePath, filePathArr[filePathArr.length - 1]);
                                //保存缩略图路径
                                temporaryFilePathList.add(temporaryFilePath);
                                //保存新的缩略图路径
                                newFilePathList.add(newFilePath);
                                //赋值新缩略图路径
                                multimedia.setThumbnail(newFilePath);
                            }
                        } else {
                            isFailure = true;
                            msgList.add(msgTitle + ",第" + row + "行," + " 缩略图（" + oldThumbnailPath + "）不存在");
                        }
                    } else {
                        isFailure = true;
                        msgList.add(msgTitle + ",第" + row + "行," + " 缩略图不为空且压缩包文件为空");
                    }
                }
                //格式化日期
                String oldReleaseDate = multimedia.getReleaseDate();
                if (StringUtils.isNotBlank(oldReleaseDate)) {
                    String releaseDate = DateUtils.updateDateSeparator(oldReleaseDate);
                    if (!DateUtils.isDate(releaseDate)) {
                        isFailure = true;
                        msgList.add(msgTitle + ",第" + row + "行," + " 发布日期（" + oldReleaseDate + "）格式错误，格式为：" + DateUtils.YYYY_MM_DD);
                    }
                    multimedia.setReleaseDate(releaseDate);
                }
                treatingAfterMultimediaList.add(multimedia);
            }
        }
        if (isFailure) {
            return msgList;
        } else {
            CacheUtils.put("peopleImportTreatingTemporaryFilePathList-" + userName, temporaryFilePathList);
            CacheUtils.put("peopleImportTreatingNewFilePathList-" + userName, newFilePathList);
            CacheUtils.put("importTreatingNewAfterMultimediaList-" + userName, treatingAfterMultimediaList);
        }
        return null;
    }

    /**
     * 导入多媒体信息
     *
     * @param updateSupport 是否支持更新, 如果已存在, 则进行更新
     * @param operName      操作用户
     * @param msgTitle      提示标题
     * @return 结果
     */
    @Override
    public String importMultimedia(boolean updateSupport, String operName, String msgTitle) {
        Date nowDate = DateUtils.getNowDate();
        long insertCount = 0;
        long updateCount = 0;
        List<IdwMultimedia> multimediaList = (List<IdwMultimedia>) CacheUtils.get("importTreatingNewAfterMultimediaList-" + operName);
        if (StringUtils.isNull(multimediaList) || multimediaList.size() < 1) {
            return null;
        }
        for (IdwMultimedia multimedia : multimediaList) {
            if (StringUtils.isNotNull(multimedia)) {
                if (StringUtils.isNull(multimedia.getMediaId())) {
                    // 新增
                    insertCount++;
                    multimedia.setCreateBy(operName);
                    multimedia.setCreateTime(nowDate);
                    idwMultimediaMapper.insertIdwMultimedia(multimedia);
                } else if (updateSupport) {
                    //更新
                    updateCount++;
                    multimedia.setUpdateBy(operName);
                    multimedia.setUpdateTime(nowDate);
                    idwMultimediaMapper.updateIdwMultimedia(multimedia);
                }
            }
        }
        CacheUtils.remove("importTreatingNewAfterMultimediaList-" + operName);
        return msgTitle + "共：" + multimediaList.size() + "条" + ",新增：" + insertCount + "条" + ",修改：" + updateCount + "条";
    }

    /**
     * 根据文件名称修改工作成果附件路径
     *
     * @param fileName      文件名称
     * @param fileUrl       文件路径
     * @param businessType  关联类型
     * @param associationId 关联编码
     * @param fileMd5       文件md5
     * @param source        数据来源
     * @return 结果
     */
    @Override
    public String saveBatchUploadFile(String fileName, String fileUrl, String businessType, String associationId, String fileMd5, String source) {
        if (StringUtils.isNotBlank(fileName) && StringUtils.isNotBlank(fileUrl)) {
            String userName = ShiroUtils.getUserName();
            Date nowDate = DateUtils.getNowDate();
            String[] fileNameArray = fileName.split(":_:_:");
            String[] fileUrlArray = fileUrl.split(":_:_:");
            String[] md5Array = fileMd5.split(":_:_:");
            for (int i = 0; i < fileNameArray.length; i++) {
                String name = fileNameArray[i];
                String url = fileUrlArray[i];
                String md5 = md5Array[i];
                //校验文件是否存在
                IdwMultimedia multimedia = idwMultimediaMapper.selectByBusinessTypeAndAssociationIdAndTitleAndMd5(businessType, associationId, name, md5);
                if (StringUtils.isNull(multimedia)) {
                    //数据不存在 新增
                    IdwMultimedia idwMultimedia = new IdwMultimedia();
                    idwMultimedia.setMediaType(name.substring(name.lastIndexOf(".") + 1));//文件后缀
                    idwMultimedia.setBusinessType(businessType);//关联类型
                    idwMultimedia.setAssociationId(associationId);//关联编码
                    idwMultimedia.setTitle(name);//文件名称
                    idwMultimedia.setMd5(md5);//文件md5
                    if (isPicture(name, null)) {
                        //如果是图片 将生成的缩略图路径存到 thumbnail 字段中 storagePath 存储真实上传图片路径
                        idwMultimedia.setThumbnail(url);
                        idwMultimedia.setStoragePath(url.replace(Constants.THUMBNAIL_PREFIX, ""));
                    } else {
                        idwMultimedia.setStoragePath(url);
                    }
                    idwMultimedia.setSource(source);//数据来源
                    idwMultimedia.setCreateBy(userName);
                    idwMultimedia.setCreateTime(nowDate);
                    idwMultimediaMapper.insertIdwMultimedia(idwMultimedia);
                }
            }
        }
        return "";
    }

    /**
     * 根据关联编码与关联类型查询
     *
     * @param associationCodes 关联编码
     * @param businessType     关联类型
     * @return 结果
     */
    @Override
    public List<IdwMultimedia> selectByAssociationCodes(String[] associationCodes, String businessType) {
        return idwMultimediaMapper.selectByAssociationCodes(associationCodes, businessType);
    }

    /**
     * 根据关联类型与关联编码删除
     *
     * @param businessType   关联类型
     * @param associationIds 关联编码
     * @param userName       当前登录用户
     * @param deleteTime     删除时间
     * @return 结果
     */
    @Override
    public int deleteByBusinessTypeAndAssociationIds(String businessType, String[] associationIds, String userName, String deleteTime) {
        return idwMultimediaMapper.deleteByBusinessTypeAndAssociationIds(businessType, associationIds, userName, deleteTime);
    }

    /**
     * 获取文件路径
     *
     * @param type 业务类型
     * @return 文件路径
     */
    @Override
    public List<String> getFilePath(List<String> type) {
        if (StringUtils.isNotNull(type) && type.size() > 0) {
            return idwMultimediaMapper.selectAllFilePath(type);
        } else {
            return null;
        }
    }

    /**
     * 根据编码删除
     *
     * @param type       类型
     * @param codes      编码
     * @param loginName  当年登录用户
     * @param deleteTime 删除时间
     * @return 结果
     */
    @Override
    public int deleteByCode(String type, String[] codes, String loginName, String deleteTime) {
        if ("org".equals(type)) {
            type = "organization";
        }
        if ("people".equals(type)) {
            type = "personnel";
        }
        return idwMultimediaMapper.deleteByBusinessTypeAndAssociationIds(type, codes, loginName, deleteTime);
    }
}
