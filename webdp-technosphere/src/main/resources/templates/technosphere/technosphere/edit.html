<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改技术领域')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-technosphere-edit" th:object="${idwTechnosphere}">
            <input id="id" name="id" th:field="*{id}" type="hidden">
            <div class="row">
                <div class="col-sm-8">
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-3 control-label is-required">中文名称：</label>
                                <div class="col-sm-8">
                                    <input name="nameCn" th:field="*{nameCn}" class="form-control" type="text" required>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-3 control-label">英文名称：</label>
                                <div class="col-sm-8">
                                    <input name="nameEn" th:field="*{nameEn}" class="form-control" type="text">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-3 control-label">上级名称：</label>
                                <div class="col-sm-8">
                                    <div class="input-group">
                                        <input id="treeId" name="parentId" type="hidden" th:field="*{parentId}" />
                                        <input class="form-control" type="text" onclick="selectTechnosphereTree()" id="treeName" readonly="true" th:field="*{parentName}">
                                        <span class="input-group-addon"><i class="fa fa-search"></i></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-sm-3">
                    <div class="form-group text-center">
                        <input type="hidden" name="avatar" id="avatar" th:field="*{avatar}">
                        <p class="user-info-head" onclick="avatar()">
                            <img class="img-lg" id="avatarUrl" th:src="*{avatar}" th:onerror="'this.src=\'' + @{'/img/default_technosphere.png'} + '\''">
                        </p>
                        <p><input type="file" id="avatarInput" style="display: none;"><a onclick="avatar()"></a></p>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-sm-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">关联类型：</label>
                        <div class="col-sm-8">
                            <div class="radio check-box">
                                <label><input type="radio" value="organization" name="associatedType" id="organizationCheckBox"> <i>机构</i> </label>
                            </div>
                            <div class="radio check-box">
                                <label><input type="radio" value="weaponry" name="associatedType"  id="weaponryCheckBox"> <i></i> 装备</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">关联类型：</label>
                        <div class="col-sm-8">
                            <div class="radio check-box">
                                <label><input type="radio" value="1" name="showChart" id="showChartBox"> <i>显示</i> </label>
                            </div>
                            <div class="radio check-box">
                                <label><input type="radio" value="0" name="showChart"  id="concealChartBox"> <i></i> 隐藏</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label class="col-sm-2 control-label" id="associatedCodeLabel"></label>
                <div class="col-sm-9">
                    <div class="input-group">
                        <input type="hidden" name="associatedCode" id="associatedCode">
                        <input type="text" class="form-control" id="associatedName" placeholder="可通过名称或编码查询" name="associatedName">
                        <div class="input-group-btn">
                            <button type="button" class="btn btn-white dropdown-toggle" data-toggle="dropdown">
                                <span class="caret"></span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-right" role="menu">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">中文简介：</label>
                <div class="col-sm-9">
                    <textarea name="descCn" class="form-control">[[*{descCn}]]</textarea>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">英文简介：</label>
                <div class="col-sm-9">
                    <textarea name="descEn" class="form-control">[[*{descEn}]]</textarea>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: bootstrap-suggest-js" />
    <script th:inline="javascript">
        let prefix = ctx + "technosphere/technosphere";
        $("#form-technosphere-edit").validate({
            focusCleanup: true
        });

        window.onload = function (){
            $('#associatedCode').val([[${idwTechnosphere.associatedCode}]])
            $('#associatedName').val([[${idwTechnosphere.associatedName}]])
        }
        let associatedType = [[${idwTechnosphere.associatedType}]]
        if (associatedType === 'weaponry'){
            $('#weaponryCheckBox').attr('checked',true);
        } else {
            $('#organizationCheckBox').attr('checked',true);
        }

        let showChart = [[${idwTechnosphere.showChart}]]
        if (showChart === 1){
            $('#showChartBox').attr('checked',true);
        } else {
            $('#concealChartBox').attr('checked',true);
        }

        //关联类型改变
        $('input[type=radio][name=associatedType]').on('ifChecked', function(event){
            loadBsSuggest()
        });

        loadBsSuggest()

        //根据关联类型加载不同搜索下拉框
        function loadBsSuggest() {
            let associatedType = $('input[name=associatedType]:checked').val();
            if (associatedType === 'weaponry'){
                document.getElementById('associatedCodeLabel').innerHTML = '关联装备名称：'
                let  effectiveFieldsAlias = {weaponryCode:'装备编码', nameCn:'中文名称', nameEn:'英文名称'}
                let effectiveFields = ['weaponryCode','nameCn','nameEn']
                associatedNameBsSuggest('weaponryCode', 'nameCn', effectiveFields, effectiveFieldsAlias, ctx +"weaponry/common/selectByKeywordExcludeWeaponryCode?weaponryCode=''&keyword=");
            } else {
                document.getElementById('associatedCodeLabel').innerHTML = '关联机构名称：'
                let  effectiveFieldsAlias = {orgCode: '机构编码', orgType: '机构类型', orgNameCn: '中文名称', orgNameEn: '英文名称'}
                let effectiveFields = ['orgCode','orgType','orgNameCn','orgNameEn']
                associatedNameBsSuggest('orgCode', 'orgNameCn', effectiveFields, effectiveFieldsAlias, ctx + "organization/org/selectByKeywordAndIncludeOrgTypes?keyword=");
            }
        }

        //构建关联机构/装备搜索下拉框
        function associatedNameBsSuggest(idField, keyField, effectiveFields, effectiveFieldsAlias, url) {
            $("#associatedCode").val('');
            $('#associatedName').val('');
            $("input#associatedName").bsSuggest("destroy");    // 销毁插件
            $("#associatedName").bsSuggest({
                url: url,
                getDataMethod: 'firstByUrl',
                autoSelect: false,// 键盘向上/下方向键时，是否自动选择值
                listStyle: {
                    'padding-top': 0,
                    'max-height': '200px',
                    'max-width': '300px',
                    'overflow': 'auto',
                    'width': 'auto',
                    'transition': '0.3s',
                    '-webkit-transition': '0.3s',
                    '-moz-transition': '0.3s',
                    '-o-transition': '0.3s'
                },//列表的样式控制
                idField: idField,//每组数据的哪个字段作为 data-id，优先级高于 indexId 设置
                keyField: keyField,//每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置
                effectiveFields: effectiveFields,//设置展示字段
                effectiveFieldsAlias: effectiveFieldsAlias//设置字段别名
            }).on('onSetSelectValue', function (e, keyword, result) {// 当从下拉菜单选取值时触发，并传回设置的数据到第二个参数
                //选择后隐藏下拉框
                $("#associatedName").bsSuggest("hide");
                $("#associatedCode").val(result.orgCode ? result.orgCode : result.weaponryCode);
            }).on("blur", function (e) {//当无匹配项且失去焦点时清除编码
                let background = $('#associatedName').css("background-color");
                if (background == 'rgba(255, 0, 0, 0.1)'){
                    $("#associatedCode").val('');
                    $('#associatedName').css("background-color", 'rgb(255, 255, 255)')
                }
            });
        }

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-technosphere-edit').serialize());
            }
        }

        /*技术领域-新增-选择父部门树*/
        function selectTechnosphereTree() {
            let options = {
                title: '技术领域选择',
                width: "380",
                url: prefix + "/selectTechnosphereTree/" + $("#id").val(),
                callBack: doSubmit
            };
            $.modal.openOptions(options);
        }

        function doSubmit(index, layero){
            let body = layer.getChildFrame('body', index);
               $("#treeId").val(body.find('#treeId').val());
               $("#treeName").val(body.find('#treeName').val());
               layer.close(index);
        }

        // 上传技术领域头像
        function avatar() {
            $('#avatarInput').trigger('click');
        }
        $("#avatarInput").change(function () {
            var data = new FormData();
            data.append("file", $("#avatarInput")[0].files[0]);
            data.append("type", "technosphere");
            $.ajax({
                type: "POST",
                url: ctx + "common/upload/img",
                data: data,
                cache: false,
                contentType: false,
                processData: false,
                dataType: 'json',
                success: function(result) {
                    if (result.code == web_status.SUCCESS) {
                        $("#avatarUrl").attr("src",result.url)
                        $("#avatar").val(result.url)
                    }
                },
                error: function(error) {
                    alert("图片上传失败。");
                }
            });
        });
    </script>
</body>
</html>