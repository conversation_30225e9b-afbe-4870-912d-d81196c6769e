/* -*- Mode: CSS; tab-width: 2; indent-tabs-mode: nil; -*- */
/* vim:set ft=css ts=2 sw=2 sts=2 autoindent: */

/* Styling for annotations */

/* alternative font test */
/*
text {
  font-size: 13px;
  font-family: helvetica,arial,freesans,clean,sans-serif;
}
*/

#svg {
    margin: 34px auto 100px;
    padding-top: 15px;
}

.center_wrapper {
    display: table;
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
}
.center_wrapper > div {
    display: table-cell;
    vertical-align: middle;
}
.center_wrapper > div > div {
    /* match jQuery UI .ui-widget-content color */
    color: #2e6e9e;
    margin-left: auto;
    margin-right: auto;
    width: 30em;
    font-size: 12px;
    font-family: 'Liberation Sans', Verdana, Arial, Helvetica, sans-serif;
}
.center_wrapper > div > div h1 {
    text-align: center;
    font-size: 14px;
}
#no_svg_wrapper {
    display: none;
}

svg {
    width: 100%;
    height: 1px;
    border: 1px solid #7fa2ff;
    font-size: 15px;
    /* font-size: 14px; */
}
svg.reselect {
    border: 1px solid #ff3333;
}

/* "basic" font */
text {
    font-size: 13px;
    font-family: 'Liberation Sans', Verdana, Arial, Helvetica, sans-serif;
}

@-webkit-keyframes pulse {
    0% { color: #9999ff; margin-top: 0; }
    50% { color: #ffffff; margin-top: 7px; }
    100% { color: #9999ff; margin-top: 0; }
}

path {
    pointer-events: none;
}

/* "basic" font */
.span text {
    /*  font-size: 10.5px; */
    font-size: 10px;
    text-anchor: middle;
    font-family: 'PT Sans Caption', sans-serif;
    pointer-events: none;
}

/* this should likely match span font */
.span_type_label {
    font-size: 11px;
    font-family: 'PT Sans Caption', sans-serif;
}
/* this should likely match arc font */
.arc_type_label {
    font-size: 11px;
    font-family: 'PT Sans Caption', sans-serif;
}
.attribute_type_label .ui-button-text {
    font-size: 11px;
    font-family: 'PT Sans Caption', sans-serif;
}

.span rect {
    stroke-width: 0.75;
}

.glyph {
    fill: #444444;
    font-family: sans-serif;
    font-weight: bold;
}
.attribute_warning {
    stroke: red;
}

.span rect.False_positive {
    stroke: #ff4141;
    stroke-width: 2;
}

.shadow_True_positive {
    fill: #00ff00;
}

.shadow_False_positive {
    fill: #ff4141;
}

#commentpopup.comment_False_positive {
    background-color: #ff4141;
}

.span rect.False_negative {
    stroke: #c20000;
    fill: #ffffff;
    stroke-width: 2;
}

.shadow_False_negative {
    fill: #c20000;
}

#commentpopup.comment_False_negative {
    background-color: #c20000;
}

.span rect.AnnotationError {
    stroke-width: 1;
}

.shadow_AnnotationError {
    fill: #ff0000;
}

#commentpopup.comment_AnnotationError {
    background-color: #ff7777;
}

.span rect.AnnotationWarning {
    stroke-width: 1;
}

.shadow_AnnotationWarning {
    fill: #ff8800;
}

#commentpopup.comment_AnnotationWarning {
    background-color: #ff9900;
}

.shadow_AnnotatorNotes {
    /* fill: #00cc00; */
    fill: #3ab7ee;
}
#commentpopup.comment_AnnotatorNotes {
    /* background-color: #00cc00; */
    /* background-color: #DAF7DC; */
    background-color: #d7e7ee;
}

.shadow_Normalized {
    /* fill: #00cc00; */
    fill: #3aee37;
}
#commentpopup.comment_Normalized {
    /* background-color: #00cc00; */
    /* background-color: #DAF7DC; */
    background-color: #d7eee7;
}
rect.Normalized {
    stroke-width: 1.5;
}

.shadow_AnnotationIncomplete {
    fill: #aaaaaa;
}
.span rect.AnnotationIncomplete {
    stroke: #002200;
    stroke-width: 0.5;
    fill: #ffffff;
}
#commentpopup.comment_AnnotationIncomplete {
    background-color: #ffff77;
}

.shadow_AnnotationUnconfirmed {
    fill: #eeeeee;
}
.span rect.AnnotationUnconfirmed {
    stroke: #002200;
    stroke-width: 0.5;
    opacity : 0.5;
}
#commentpopup.comment_AnnotationUnconfirmed {
    background-color: #ddddff;
}

.span rect.True_positive {
    /* stroke: #007700; */
}

rect.shadow_EditHighlight {
    fill: #ffff99;
}
.shadow_EditHighlight_arc {
    stroke: #ffff99;
}

.span path {
    fill: none;
}

.span path.curly {
    /* 'stroke' def here blocks 'stroke' set in JS..? */
    /* stroke: grey; */
    stroke-width: 0.5;
}

.span path.boxcross {
    stroke: black;
    opacity: 0.5;
}

.arcs path {
    stroke: #989898;
    fill: none;
    stroke-width: 1;
}

.arcs .highlight path {
    stroke: #000000;
    stroke-width: 1.5;
    opacity: 1;
}
.arcs .highlight text {
    stroke: black;
    fill: black;
    stroke-width: 0.5;
}
.span.highlight rect {
    stroke-width: 2px;
}
.span rect.reselect {
    stroke-width: 2px;
}
.span rect.reselectTarget {
    stroke-width: 2px;
}
.arcs .reselect path {
    stroke-width: 2px;
    stroke: #ff0000 !important;
}
.arcs .reselect text {
    fill: #ff0000 !important;
}

.span rect.badTarget {
    stroke: #f00;
}

.arcs text {
    font-size: 9px;
    text-anchor: middle;
    font-family: 'PT Sans Caption', sans-serif;
    /* dominant-baseline: central; */
    cursor: default;
}

.background0 {
    stroke: none;
    fill: #ffffff;
}

.background1 {
    stroke: none;
    fill: #eeeeee;
}

.backgroundHighlight {
    stroke: none;
    fill: #ffff99;
}

.sentnum text {
    fill: #999999;
    text-anchor: end;
}

.sentnum path {
    stroke: #999999;
    stroke-width: 1px;
}

.span_cue {
    fill: #eeeeee !important;
}

.drag_stroke {
    stroke: black;
}
.drag_fill {
    fill: black;
}


.dialog {
    display: none;
}
#span_free_div, #arc_free_div {
    float: left;
}
fieldset {
    /* match relevant parts of jQuery UI .ui-widget-content */
    border: 1px solid #a6c9e2;
    /* round corners, same as jQuery UI .ui-corner-all */
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    border-radius: 5px;
    /* to prevent border from running into legend */
    margin-top: 5px;
}
fieldset legend {
    font-weight: bold;
    font-size: 90%;
    -moz-border-radius: 3px;
    -webkit-border-radius: 3px;
    border-radius: 3px;
    background-color: #70a8d2;
    color: white;
    padding-left: 0.5em;
    padding-right: 0.5em;
}
.label-like {
    /* for UI consistency, this should match the "fieldset legend" style;
       sorry about the duplication. */
    font-family: monospace;
    font-weight: bold;
    font-size: 90%;
    /* match jQuery UI .ui-widget-content color */
    color: #2e6e9e;
}

.accesskey {
    text-decoration: underline;
}


.shadow {
    -moz-box-shadow: 5px 5px 5px #444444;
    -webkit-box-shadow: 5px 5px 5px #444444;
    box-shadow: 5px 5px 5px #444444;
}

#span_selected, #arc_origin, #arc_target {
    font-weight: bold;
}

#commentpopup {
    font-family: 'Liberation Sans', Verdana, Arial, Helvetica, sans-serif;
    position: fixed;
    top: 0;
    left: 0;
    opacity: 0.95;
    padding: 10px;
    display: none;
    border: 1px outset #000000;
    background-color: #f5f5f9;
    /* background-color: #d7e7ee; */
    /* background-color: #eeeeee; */
    color: #000000;
    z-index: 20;
    -moz-box-shadow: 5px 5px 5px #aaaaaa;
    -webkit-box-shadow: 5px 5px 5px #aaaaaa;
    box-shadow: 5px 5px 5px #aaaaaa;
    -moz-border-radius: 3px;
    -webkit-border-radius: 3px;
    border-radius: 3px;
    max-width: 80%;
}
#more_info_readme {
    height: 350px;
}
#readme_container {
    position: relative;
}
#more_readme_button {
    padding: 2px 5px;
    position: absolute;
    top: -2px;
    right: -2px;
}

.comment_id {
    vertical-align: top;
    float:right;
    font-size: 75%;
    font-family: monospace;
    color: #333333;
}
.comment_type {
    /* font-weight: bold; */
}
.comment_text {
    font-weight: bold;
}
.comment_type_id_wrapper {
    /* padding for floating ID */
    padding-right: 2em;
}
.norm_info_label {
    font-weight: bold;
    font-size: 80%;
}
.norm_info_value {
    font-size: 80%;
}
.norm_info_img {
    float: right;
    margin-left: 1em;
}

#search_form select {
    width: 100%; /* deal with overflowing selectboxes */
}

.scroll_fset {
    height: 200px;
}
.scroll_fset fieldset {
    height: 100%;
    overflow-x: hidden;
    overflow-y: hidden;
}
/* HACK to display <legend> properly */
.scroll_fset {
    margin-bottom: 2.5em;
}
.scroll_fset fieldset {
    padding-bottom: 2em;
}
/* end HACK */
.scroll_fset div.scroller {
    overflow: auto;
    width: 100%;
    height: 100%;
}

#span_highlight_link, #arc_highlight_link, #viewspan_highlight_link {
    float: right;
}

.unselectable {
    -moz-user-select: -moz-none;
    -khtml-user-select: none;
    -webkit-user-select: none;
    -o-user-select: none;
    user-select: none;
    cursor: default;
}
@font-face {
    font-family: 'Liberation Sans';
    font-style: normal;
    font-weight: normal;
    src: local('Liberation Sans'), local('Liberation-Sans'), url('static/fonts/Liberation_Sans-Regular.ttf') format('truetype');
}
@font-face {
    font-family: 'PT Sans Caption';
    font-style: normal;
    font-weight: normal;
    src: local('PT Sans Caption'), local('PTSans-Caption'), url('static/fonts/PT_Sans-Caption-Web-Regular.ttf') format('truetype');
}

/* Apple iPad, iPhone, iPod */

* {
    -webkit-tap-highlight-color: rgba(0,0,0,0);
    -webkit-text-size-adjust: none;
    select: none;
}

.span rect.AddedAnnotation {
    stroke: #ff4141;
    stroke-width: 2;
}
.shadow_AddedAnnotation {
    fill: #ff4141;
}
#commentpopup.comment_AddedAnnotation {
    background-color: #ffcccc;
}
.span rect.MissingAnnotation {
    stroke: #ffffff;
    stroke-width: 2;
}
.shadow_MissingAnnotation {
    fill: #ff4141;
    opacity: 0.3;
}
#commentpopup.comment_MissingAnnotation {
    background-color: #ffcccc;
}
.span rect.MissingAnnotation + text {
    opacity: 0.5;
}
.span rect.ChangedAnnotation {
    stroke: #ffff99;
    stroke-width: 2;
}
.shadow_ChangedAnnotation {
    fill: #ff4141;
}
#commentpopup.comment_ChangedAnnotation {
    background-color: #ffcccc;
}