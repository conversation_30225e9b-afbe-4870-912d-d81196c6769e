<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增国家信息')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-country-add">
            <div class="row">
                <div class="col-sm-8">
                    <div class="form-group">
                        <label class="col-sm-3 control-label is-required">编码：</label>
                        <div class="col-sm-8">
                            <input name="code" class="form-control" type="text" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">中文名称：</label>
                        <div class="col-sm-8">
                            <input name="nameCn" class="form-control" type="text">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">英文名称：</label>
                        <div class="col-sm-8">
                            <input name="nameEn" class="form-control" type="text">
                        </div>
                    </div>
                </div>
                <div class="col-sm-4">
                    <div class="form-group text-center" title="上传国旗">
                        <input type="hidden" name="flag" id="flag">
                        <p class="user-info-head" onclick="flag()">
                            <img class="img-lg" id="flagUrl" th:src="@{/img/default_flag.png}" th:onerror="'this.src=\'' + @{'/img/default_flag.png'} + '\''" style="height: 92px; width: 138px;">
                        </p>
                        <p><input type="file" id="flagInput" style="display: none;"><a onclick="flag()"></a></p>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-sm-8">
                    <div class="form-group">
                        <label class="col-sm-3 control-label">别名：</label>
                        <div class="col-sm-8">
                            <input name="alias" class="form-control" type="text">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">Geo标识：</label>
                        <div class="col-sm-8">
                            <input name="geo" class="form-control" type="text">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">经度：</label>
                        <div class="col-sm-8">
                            <input name="longitude" id="longitude" onchange="calculateLongitude(this.value)" placeholder="输入经纬度,系统自动计算" class="form-control" type="text" number="true">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">纬度：</label>
                        <div class="col-sm-8">
                            <input name="latitude" id="latitude" onchange="calculateLatitude(this.value)" placeholder="输入经纬度,系统自动计算" class="form-control" type="text" number="true">
                        </div>
                    </div>
                </div>
                <div class="col-sm-4">
                    <div class="form-group text-center" title="上传地图">
                        <input type="hidden" name="map" id="map">
                        <p class="user-info-head" onclick="map()">
                            <img class="img-lg" id="mapUrl" th:src="@{/img/default_map.png}" th:onerror="'this.src=\'' + @{'/img/default_map.png'} + '\''" style="height: 201px; width: 257px;">
                        </p>
                        <p><input type="file" id="mapInput" style="display: none;"><a onclick="map()"></a></p>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        let prefix = ctx + "country/country"
        $("#form-country-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/add", $('#form-country-add').serialize());
            }
        }

        //上传国旗
        function flag() {
            $('#flagInput').trigger('click');
        }
        $("#flagInput").change(function () {
            var data = new FormData();
            data.append("file", $("#flagInput")[0].files[0]);
            $.ajax({
                type: "POST",
                url: ctx + "common/upload/img",
                data: data,
                cache: false,
                contentType: false,
                processData: false,
                dataType: 'json',
                success: function(result) {
                    if (result.code == web_status.SUCCESS) {
                        $("#flagUrl").attr("src",result.url)
                        $("#flag").val(result.url)
                    }
                },
                error: function(error) {
                    alert("国旗上传失败。");
                }
            });
        });
        //上传地图
        function map() {
            $('#mapInput').trigger('click');
        }
        $("#mapInput").change(function () {
            var data = new FormData();
            data.append("file", $("#mapInput")[0].files[0]);
            $.ajax({
                type: "POST",
                url: ctx + "common/upload/img",
                data: data,
                cache: false,
                contentType: false,
                processData: false,
                dataType: 'json',
                success: function(result) {
                    if (result.code == web_status.SUCCESS) {
                        $("#mapUrl").attr("src",result.url)
                        $("#map").val(result.url)
                    }
                },
                error: function(error) {
                    alert("国旗上传失败。");
                }
            });
        });

        let lonLatRe = /^(-?\d+)(\.\d+)?$/;
        //计算经纬度
        function calculateLongitude(value){
            $('#longitude').val('');
            if (value.indexOf("N") > 0 || value.indexOf("S") > 0 || value.indexOf("E") > 0 || value.indexOf("W") > 0){
                if (value.indexOf("E") > 0 || value.indexOf("W") > 0){
                    $('#longitude').val(calculateLon(value));
                }
                if (value.indexOf("N") > 0 || value.indexOf("S") > 0){
                    $('#latitude').val(calculateLat(value));
                }
            } else if (lonLatRe.test(value)){
                $('#longitude').val((value + 'longitude').replace(value.substring(value.indexOf('.') + 7) + 'longitude', ''));
                let longitude = $('#longitude').val();//经度  -180~180
                let strings = longitude.split('.');
                let integer = strings[0];
                if ( integer < -180 || integer > 180){
                    $('#longitude').val('')
                    $.modal.msgError('经度格式错误（经度范围 -180~180，保留小数点后六位）！')
                } else if ((integer == 180 || integer == -180) && strings.length > 1 && parseInt(strings[1]) > 0){
                    $('#longitude').val('')
                    $.modal.msgError('经度格式错误（经度范围 -180~180，保留小数点后六位）！')
                }
            } else if (value != ''){
                $('#longitude').val('')
                $.modal.msgError('经度格式错误（经度范围 -180~180，保留小数点后六位）！')
            }
        }
        function calculateLatitude(value){
            $('#latitude').val('');
            if (value.indexOf("N") > 0 || value.indexOf("S") > 0 || value.indexOf("E") > 0 || value.indexOf("W") > 0){
                if (value.indexOf("E") > 0 || value.indexOf("W") > 0){
                    $('#longitude').val(calculateLon(value));
                }
                if (value.indexOf("N") > 0 || value.indexOf("S") > 0){
                    $('#latitude').val(calculateLat(value));
                }
            } else if (lonLatRe.test(value)){
                $('#latitude').val((value + 'latitude').replace(value.substring(value.indexOf('.') + 7) + 'latitude', ''));
                let latitude = $('#latitude').val();//纬度  -90~90
                let strings = latitude.split('.');
                let integer = strings[0];
                if ( integer < -90 || integer > 90){
                    $('#latitude').val('')
                    $.modal.msgError('纬度格式错误（纬度范围 -90~90，保留小数点后六位）！')
                } else if ((integer == 90 || integer == -90) && strings.length > 1 && parseInt(strings[1]) > 0){
                    $('#latitude').val('')
                    $.modal.msgError('纬度格式错误（纬度范围 -90~90，保留小数点后六位）！')
                }
            } else if (value != ''){
                $('#latitude').val('')
                $.modal.msgError('格式错误（纬度范围 -90~90，保留小数点后六位）！')
            }
        }
    </script>
</body>
</html>